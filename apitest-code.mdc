# API接口请求参数及业务状态码文档

## 📋 文档说明
本文档基于Controller系统性扫描生成，严格按照用户提供的模板格式，包含所有API接口的请求参数及业务状态码信息。

---

### **AuthController (6)**
- [ ] **1** 用户注册 `POST /api/auth/register`
  - 请求参数：`username, password, email`
  - 成功响应：`200` - 注册成功
  - 错误响应：`422` - 参数验证失败, `409` - 用户已存在

- [ ] **2** 用户登录 `POST /api/auth/login`
  - 请求参数：`username, password`
  - 成功响应：`200` - 登录成功
  - 错误响应：`422` - 参数验证失败, `401` - 认证失败

- [ ] **3** Token验证 `GET /api/auth/verify`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - Token验证成功
  - 错误响应：`401` - Token无效或过期

- [ ] **4** 用户登出 `POST /api/auth/logout`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 登出成功
  - 错误响应：`401` - 认证失败

- [ ] **5** 忘记密码 `POST /api/auth/forgot-password`
  - 请求参数：`email`
  - 成功响应：`200` - 重置邮件发送成功
  - 错误响应：`422` - 参数验证失败, `404` - 用户不存在

- [ ] **6** 重置密码 `POST /api/auth/reset-password`
  - 请求参数：`token, new_password, password_confirmation`
  - 成功响应：`200` - 密码重置成功
  - 错误响应：`422` - 参数验证失败, `400` - 令牌无效

### **UserController (4)**
- [ ] **7** 获取用户个人信息 `GET /api/user/profile`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **8** 更新用户资料 `PUT /api/user/profile`
  - 请求参数：`nickname, email, avatar` (可选)
  - 成功响应：`200` - 更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `500` - 系统错误

- [ ] **9** 更新用户偏好设置 `PUT /api/user/preferences`
  - 请求参数：`language, timezone, email_notifications, push_notifications, ai_preferences, ui_preferences, workflow_preferences, default_ai_model, auto_save_interval, show_tutorials`
  - 成功响应：`200` - 更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败

- [ ] **10** 获取用户偏好设置 `GET /api/user/preferences`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

### **PointsController (3)**
- [ ] **11** 积分余额查询 `GET /api/points/balance`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 查询成功
  - 错误响应：`401` - 认证失败

- [ ] **12** 积分充值 `POST /api/points/recharge`
  - 请求参数：`amount, payment_method`
  - 成功响应：`200` - 充值成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 充值失败

- [ ] **13** 积分交易记录 `GET /api/points/transactions`
  - 请求参数：`page, per_page, status, business_type` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

### **CreditsController (3)**
- [ ] **14** 积分预检查 `POST /api/credits/check`
  - 请求参数：`amount, business_type, business_id` (可选)
  - 成功响应：`200` - 积分检查完成
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败

- [ ] **15** 积分冻结 `POST /api/credits/freeze`
  - 请求参数：`amount, business_type, business_id, timeout_seconds` (可选)
  - 成功响应：`200` - 积分冻结成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败

- [ ] **16** 积分返还 `POST /api/credits/refund`
  - 请求参数：`freeze_id, return_reason` (可选)
  - 成功响应：`200` - 积分返还成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败

### **UserGrowthController (10)**

- [ ] **17** 获取用户成长信息 `GET /api/user-growth/profile`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **18** 获取排行榜 `GET /api/user-growth/leaderboard`
  - 请求参数：`type, period, limit` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **19** 获取每日任务 `GET /api/user-growth/daily-tasks`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **20** 获取成长历史 `GET /api/user-growth/history`
  - 请求参数：`type, date_from, date_to, page, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **21** 获取统计信息 `GET /api/user-growth/statistics`
  - 请求参数：`period` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **22** 设置成长目标 `POST /api/user-growth/goals`
  - 请求参数：`goals` (数组，包含type和target)
  - 成功响应：`200` - 设置成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败

- [ ] **23** 获取推荐 `GET /api/user-growth/recommendations`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **24** 获取里程碑 `GET /api/user-growth/milestones`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **25** 完成每日任务 `POST /api/user-growth/daily-tasks/{id}/complete`
  - 请求参数：`task_id, completion_data` (可选)
  - 成功响应：`200` - 完成成功
  - 错误响应：`401` - 认证失败, `404` - 任务不存在

- [ ] **26** 完成成就 `POST /api/user-growth/achievements/{id}/complete`
  - 请求参数：`achievement_id, progress_data` (可选)
  - 成功响应：`200` - 完成成功
  - 错误响应：`401` - 认证失败, `404` - 成就不存在

### **NotificationController (6)**
- [ ] **27** 获取用户通知列表 `GET /api/notifications`
  - 请求参数：`type, status, page, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **28** 标记通知为已读 `PUT /api/notifications/mark-read`
  - 请求参数：`notification_ids` (数组)
  - 成功响应：`200` - 标记成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败

- [ ] **29** 标记所有通知为已读 `PUT /api/notifications/mark-all-read`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 标记成功
  - 错误响应：`401` - 认证失败

- [ ] **30** 获取通知统计 `GET /api/notifications/stats`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **31** 发送通知 `POST /api/notifications/send`
  - 请求参数：通知相关参数
  - 成功响应：`200` - 发送成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败

- [ ] **32** 删除通知 `DELETE /api/notifications/{id}`
  - 请求参数：通知ID (路径参数)
  - 成功响应：`200` - 删除成功
  - 错误响应：`401` - 认证失败, `404` - 通知不存在

### **ConfigController (7)**
- [ ] **33** 获取配置列表 `GET /api/config`
  - 请求参数：`category, key, page, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足

- [ ] **34** 获取公开配置 `GET /api/config/public`
  - 请求参数：无
  - 成功响应：`200` - 获取成功
  - 错误响应：无 (公开访问)

- [ ] **35** 更新配置 `PUT /api/config/{id}`
  - 请求参数：`value, description` (可选)
  - 成功响应：`200` - 更新成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足, `422` - 参数验证失败

- [ ] **36** 批量更新配置 `PUT /api/config/batch`
  - 请求参数：`configs` (数组)
  - 成功响应：`200` - 批量更新成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足, `422` - 参数验证失败

- [ ] **37** 重置配置 `POST /api/config/{id}/reset`
  - 请求参数：配置ID (路径参数)
  - 成功响应：`200` - 重置成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足, `404` - 配置不存在

- [ ] **38** 获取配置历史 `GET /api/config/{id}/history`
  - 请求参数：配置ID (路径参数), `page, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足, `404` - 配置不存在

- [ ] **39** 验证配置 `POST /api/config/validate`
  - 请求参数：`key, value, type` (可选)
  - 成功响应：`200` - 验证成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败


