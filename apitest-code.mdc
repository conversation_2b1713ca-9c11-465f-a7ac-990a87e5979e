# API接口请求参数及业务状态码文档

## 📋 文档说明
本文档基于Controller系统性扫描生成，严格按照用户提供的模板格式，包含所有API接口的请求参数及业务状态码信息。

---

### **AuthController (6)**
- [ ] **1** 用户注册 `POST /api/auth/register`
  - 请求参数：`username, password, email`
  - 成功响应：`200` - 注册成功
  - 错误响应：`422` - 参数验证失败, `409` - 用户已存在

- [ ] **2** 用户登录 `POST /api/auth/login`
  - 请求参数：`username, password`
  - 成功响应：`200` - 登录成功
  - 错误响应：`422` - 参数验证失败, `401` - 认证失败

- [ ] **3** Token验证 `GET /api/auth/verify`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - Token验证成功
  - 错误响应：`401` - Token无效或过期

- [ ] **4** 用户登出 `POST /api/auth/logout`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 登出成功
  - 错误响应：`401` - 认证失败

- [ ] **5** 忘记密码 `POST /api/auth/forgot-password`
  - 请求参数：`email`
  - 成功响应：`200` - 重置邮件发送成功
  - 错误响应：`422` - 参数验证失败, `404` - 用户不存在

- [ ] **6** 重置密码 `POST /api/auth/reset-password`
  - 请求参数：`token, new_password, password_confirmation`
  - 成功响应：`200` - 密码重置成功
  - 错误响应：`422` - 参数验证失败, `400` - 令牌无效

### **UserController (4)**
- [ ] **7** 获取用户个人信息 `GET /api/user/profile`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

- [ ] **8** 更新用户资料 `PUT /api/user/profile`
  - 请求参数：`nickname, email, avatar` (可选)
  - 成功响应：`200` - 更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `500` - 系统错误

- [ ] **9** 更新用户偏好设置 `PUT /api/user/preferences`
  - 请求参数：`language, timezone, email_notifications, push_notifications, ai_preferences, ui_preferences, workflow_preferences, default_ai_model, auto_save_interval, show_tutorials`
  - 成功响应：`200` - 更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败

- [ ] **10** 获取用户偏好设置 `GET /api/user/preferences`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败

### **PointsController (3)**
- [ ] **11** 积分余额查询 `GET /api/points/balance`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 查询成功
  - 错误响应：`401` - 认证失败

- [ ] **12** 积分充值 `POST /api/points/recharge`
  - 请求参数：`amount, payment_method`
  - 成功响应：`200` - 充值成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 充值失败

- [ ] **13** 积分交易记录 `GET /api/points/transactions`
  - 请求参数：`page, per_page, status, business_type` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败
