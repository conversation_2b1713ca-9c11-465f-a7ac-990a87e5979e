---
description: 
globs: 
alwaysApply: true
---
# 架构规范

## 项目简要信息

1. 当前开发环境：Windows11 + python 3.12 + Nginx1.26.2 + PHP 8.1.29 + MySQL 8.0.12 + redis 7.4.2

2. 将来生产环境：
   管理后台：centos 8 stream + Nginx1.26.2 + PHP 8.1.29 + MySQL 8.0.12 + redis 7.4.2
   工具api接口服务：centos 8 stream + Nginx1.26.2 + PHP 8.1.29 + MySQL 8.0.12 + redis 7.4.2
   WEB网页工具：centos 8 stream + Nginx1.26.2
   Python用户终端工具：Windows 和 Mac
   虚拟第三方AI API接口服务：本地开发阶段根据第三方AI的API接口文档模拟接口和返回处理支持本地项目开发测试用

3. 项目目录：
   管理后台：php/backend/
   工具api接口服务：php/api/ (控制器层: app/Http/Controllers/Api ↔ 服务层: app/Services)
   WEB网页工具：php/web/
   Python用户终端工具：python/
   虚拟第三方AI API接口服务：php/aiapi/

4. 采用技术：
   管理后台：已有基于laravel10开发的管理后台
   工具api接口服务：已有基于lumen10开发的api接口服务
   WEB网页工具：响应式ui布局（支持PC和移动），全静态前端调用工具api接口服务
   Python用户终端工具：主要基于python进行开发
   虚拟第三方AI API接口服务：根据第三方AI的API文档进行模拟接收和模拟返回

5. 后台信息：
   管理后台：https://tool.tiptop.cn/backend
   管理帐号：tiptop / 123456
   数据库名称：ai_tool
   数据库用户：ai_tool / rootroot

### 🔧 项目依赖
   工具api接口服务: 依赖 “虚拟第三方AI API接口服务” 项目提供api接口支持本地开发为其它项目调用api接口。
   Python用户终端工具:依赖 “工具api接口服务” 项目提供api接口实现所有功能的开发。
   WEB网页工具:依赖 “工具api接口服务” 项目提供api接口实现所有功能的开发。
   管理后台:依赖 “工具api接口服务” 项目提供api接口实现所有功能的开发。


7. 工具api接口服务的接口测试网址：https://api.tiptop.cn
   测试流程：
   帐号/密码：YESxlx / YESxlx

8. WEB网页工具：https://web.tiptop.cn


9. 虚拟第三方AI API接口服务：https://aiapi.tiptop.cn
   测试页面：https://aiapi.tiptop.cn/test.html

## 📊 AI模型配置信息

### 🤖 支持的AI平台列表
- **LiblibAI**: 图像生成专业平台
- **KlingAI**: 视频生成领导者
- **MiniMax**: 多模态AI平台
- **DeepSeek**: 剧情生成和分镜脚本专家
- **火山引擎豆包**: 专业语音AI平台

### 🚫 禁止使用的模型
- OpenAI、GPT系列模型
- anthropic、Claude系列模型

### 🎯 业务模型配置矩阵

#### 图像生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 专业图像生成、ComfyUI工作流、风格转换
- **KlingAI**: 高质量图像生成、图像放大、图像修复
- **MiniMax**: 多模态图像生成、图像理解

#### 视频生成业务
**可选平台**: KlingAI + MiniMax
- **KlingAI**: 专业视频生成、图像转视频、视频扩展
- **MiniMax**: 多模态视频生成、视频理解

#### 剧情生成业务
**可选平台**: DeepSeek + MiniMax
- **DeepSeek**: 专业剧情创作、分镜脚本、角色对话
- **MiniMax**: 多模态剧情生成、情节构建

#### 角色生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 角色形象生成、角色设计
- **KlingAI**: 角色动画生成、角色表情
- **MiniMax**: 角色属性生成、角色对话

#### 风格生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 艺术风格生成、风格转换
- **KlingAI**: 视觉风格生成、风格应用
- **MiniMax**: 多模态风格生成、风格理解

#### 音效生成业务
**可选平台**: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业音效处理、音效合成
- **MiniMax**: 多模态音效生成、音效理解

#### 音色生成业务
**可选平台**: MiniMax + 火山引擎豆包
- **MiniMax**: 音色设计、音色合成
- **火山引擎豆包**: 声音复刻、音色处理

#### 音乐生成业务
**可选平台**: MiniMax
- **MiniMax**: 专业音乐生成、音乐创作、音乐理解

## 🔐 **Token认证机制规范** <!-- 🚨 CogniArch新增：基于/api/batch/tasks/status接口测试验证 -->

### 📋 **AuthService认证机制**
工具API接口服务使用统一的AuthService认证机制，支持两种Token传递方式：

#### **✅ 支持的认证方式**
1. **Bearer Token方式** (推荐)：
   ```
   Authorization: Bearer {token}
   ```
   - 标准HTTP Bearer Token格式
   - 符合RFC 6750规范
   - 适用于所有API接口

2. **URL参数方式** (兼容性)：
   ```
   ?token={token}
   ```
   - 通过URL参数传递Token
   - 便于快速测试和调试
   - 与Bearer Token方式等效

#### **❌ 不支持的认证方式**
- **无Bearer前缀的Authorization头**: `Authorization: {token}` ❌ 失败
- **无认证访问**: 直接访问受保护接口 ❌ 失败

#### **🔄 AuthService.extractToken()处理逻辑**
```php
// 优先级1: 从请求参数中获取token参数
$token = $request->input('token');

// 优先级2: 从Authorization头中提取Bearer Token
if (empty($token)) {
    $header = $request->header('Authorization', '');
    $position = strrpos($header, 'Bearer ');
    if ($position !== false) {
        $header = substr($header, $position + 7);
        $token = strpos($header, ',') !== false ? strstr($header, ',', true) : $header;
    }
}
```

#### **🎯 验证接口示例**
- **测试接口**: `/api/batch/tasks/status`
- **验证结果**: ✅ Bearer Token方式成功，✅ URL参数方式成功
- **错误响应**: `{"code":401,"message":"请登录后操作","data":[]}`

#### **🔒 安全特性**
- Token存储在Redis中，格式：`user:token:{user_id}`
- Token加密存储，使用ApiTokenHelper::encryptToken()
- Token有效期：30-35天随机TTL
- 支持Token失效检查和用户信息验证

#### **🚪 登出安全规范** <!-- 🚨 CogniDev更新：简化版本实施 -->
**核心原则**：确保用户登出后Token失效

**基本要求**：
1. **Token失效**：
   - 从Redis中删除用户Token记录
   - 清除格式：`user:token:{user_id}`
   - 确保Token无法再次使用

2. **错误处理**：
   - 基本的错误响应和日志记录
   - 统一的响应格式

**实现接口**：`POST /api/auth/logout`

#### **🔑 密码重置机制规范** <!-- 🚨 CogniDev更新：简化版本实施 -->
**核心原则**：提供基本的密码重置功能

**基本要求**：
1. **忘记密码流程**：
   - 验证用户邮箱是否存在
   - 生成重置令牌存储到Redis（1小时有效期）
   - 基本的错误处理和日志记录

2. **重置密码流程**：
   - 验证重置令牌有效性
   - 更新用户密码（使用password_hash加密）
   - 删除已使用的重置令牌

3. **Token验证机制**：
   - 验证用户Token有效性
   - 返回基本的用户信息

**实现接口**：
- `POST /api/auth/forgot-password`
- `POST /api/auth/reset-password`
- `GET /api/auth/verify`

## 🔐 api接口业务状态码定义规范

1、业务状态码和HTTP状态码相同的会映射到HTTP状态码，业务状态码和HTTP状态码不同的HTTP状态码将被设置为200。
2、所有的业务状态码和状态码说明必须在 php\api\app\Enums\ApiCodeEnum.php 中设置。

## 📋 工具API接口服务开发文档应用规则

### **控制器层 ↔ 服务层架构规范**

**工具API接口服务采用分层架构模式，明确控制器层与服务层的职责分离：**

#### **📁 目录结构与职责分工**
```
php/api/
├── app/Http/Controllers/Api/     # 控制器层 (Controller Layer)
│   ├── AuthController.php        # 处理HTTP请求、参数验证、响应格式化
│   ├── ImageController.php       # 接收图像相关API请求
│   ├── ProjectController.php     # 接收项目管理API请求
│   ├── WebSocketController.php   # 处理WebSocket连接与消息路由
│   └── ...
├── app/Services/                 # 服务层 (Service Layer)
│   ├── AuthService.php           # 认证业务逻辑处理
│   ├── ImageServiceController.php # 图像处理业务逻辑
│   ├── ProjectCreationController.php # 项目创建业务逻辑
│   └── ...
└── app/WebSocket/                # WebSocket服务层 (WebSocket Service Layer)
    ├── WebSocketService.php      # WebSocket业务逻辑处理
    ├── MessageHandler.php        # 消息处理与分发
    ├── ConnectionManager.php     # 连接管理与状态维护
    └── ...
```

#### **🔄 控制器层 ↔ 服务层关系模式**

**1. 配合关系 (非替换关系)**：
- `ImageController` ↔ `ImageServiceController`
- `ProjectController` ↔ `ProjectCreationController`
- `PointsController` ↔ `UnifiedPointsController`
- `WebSocketController` ↔ `WebSocketService` (WebSocket连接管理 ↔ WebSocket业务逻辑)

**2. 职责分离原则**：
- **控制器层职责**：
  - HTTP请求接收与路由处理
  - 请求参数验证与格式化
  - 响应数据格式化与返回
  - 异常处理与错误响应
  - WebSocket连接建立与消息路由

- **服务层职责**：
  - 具体业务逻辑实现
  - 数据库操作与事务管理
  - 外部API调用与集成
  - 复杂算法与数据处理

- **WebSocket服务层职责**：
  - WebSocket消息处理与分发
  - 实时通信业务逻辑
  - 连接状态管理与维护
  - AI生成进度推送服务

**3. 调用流程**：

**HTTP API调用流程**：
```
HTTP请求 → 控制器层 → 服务层 → 数据库/外部服务 → 服务层 → 控制器层 → HTTP响应
```

**WebSocket通信流程**：
```
WebSocket连接 → WebSocketController → WebSocketService → 业务逻辑处理 → 消息推送 → 客户端
```

**混合调用流程** (AI生成场景)：
```
HTTP请求 → 控制器层 → 服务层 → AI服务调用 → WebSocket推送进度 → HTTP响应结果
```

### 🎯 **分类文档应用场景明确定义**

**针对 @php/api/ 目录的"工具API接口服务"开发，必须严格按照以下规则使用分类文档：**

#### **📚 三大核心文档体系**

1. **🆕 新功能开发文档**：`@.cursor/rules/dev-api-guidelines-add.mdc`
2. **🔧 问题修复文档**：`@.cursor/rules/dev-api-guidelines-edit.mdc`
3. **🤖 AI服务对接文档**：`@.cursor/rules/dev-aiapi-guidelines.mdc`

#### **🆕 新功能开发场景**
**主要文档**：`@.cursor/rules/dev-api-guidelines-add.mdc`
**辅助文档**：`@.cursor/rules/dev-aiapi-guidelines.mdc`（当涉及AI功能时）

**适用情况**：
- ✅ **新增API接口开发**：创建全新的控制器、服务层、中间件
- ✅ **新增数据库表设计**：设计新的数据表结构和迁移程序
- ✅ **新增业务模块开发**：实现全新的业务功能模块
- ✅ **新增WebSocket处理器**：开发Python工具专用的WebSocket服务
- ✅ **新增AI服务集成**：集成新的AI生成服务和功能（必须配合dev-aiapi-guidelines.mdc）
- ✅ **扩展性架构设计**：设计可扩展的系统架构
- ✅ **高级功能扩展**：用户成长路径、个性化推荐、AI模型管理等

#### **🔧 问题修复场景**
**主要文档**：`@.cursor/rules/dev-api-guidelines-edit.mdc`
**辅助文档**：`@.cursor/rules/dev-aiapi-guidelines.mdc`（当修复AI相关问题时）

**适用情况**：
- ✅ **架构违规修复**：修复WebSocket边界违规、职责边界不清等问题
- ✅ **性能指标修正**：统一并发用户数为1000、响应时间优化等
- ✅ **安全机制强化**：积分系统安全加固、认证机制完善
- ✅ **接口规范修正**：移除违规接口、修正API设计
- ✅ **数据库结构修复**：修正表字段、索引优化、关系调整
- ✅ **代码质量提升**：重构现有代码、优化业务逻辑
- ✅ **AI服务调用问题修复**：修复AI API调用失败、超时、格式错误等问题

#### **🤖 AI服务对接场景**
**主要文档**：`@.cursor/rules/dev-aiapi-guidelines.mdc`
**配合文档**：根据具体需求选择add或edit文档

**适用情况**：
- ✅ **AI API接口调用**：调用DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包等AI服务 <!-- 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax -->
- ✅ **AI服务集成开发**：集成文本生成、图像生成、语音合成、视频生成功能
- ✅ **AI服务错误处理**：处理AI API调用失败、超时、格式错误等问题
- ✅ **AI服务性能优化**：优化AI API调用性能、实现负载均衡、服务降级
- ✅ **AI服务监控**：监控AI API调用状态、成功率、响应时间等指标

**包含内容**：
- 🤖 **5个AI平台完整接口规范**：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包
- 🤖 **87个AI API接口**：文本生成、图像生成、语音合成、视频生成、音效处理、音频混合等
- 🤖 **分平台功能支持详情**： <!-- 🔧 LongDev1补充：基于LongChec2审查，补充分平台功能详情 -->
  - **DeepSeek**: 文本生成、剧情生成、角色生成（双平台支持）
  - **LiblibAI**: 图像生成（三平台支持）
  - **KlingAI**: 图像生成、视频生成（多平台支持）
  - **MiniMax**: 语音合成、音乐生成、音效生成、音色生成、图像生成、视频生成、文本生成（全功能平台）
  - **火山引擎豆包**: 语音合成、音效生成、音色生成、音频混合（专业音频平台）
- 🤖 **AI生成控制器索引**： <!-- 🔧 LongDev1补充：基于LongChec2审查，补充新增控制器索引 -->
  - **MusicController**: 音乐生成控制器（MiniMax平台，5个接口）
  - **SoundController**: 音效生成控制器（火山引擎豆包+MiniMax双平台，4个接口）
  - **TimbreController**: 音色生成控制器（双平台支持，4个接口）
  - **VideoController**: 视频生成控制器（KlingAI+MiniMax双平台，4个接口）
  - **ImageController**: 图像编辑控制器（LiblibAI+KlingAI+MiniMax三平台，6个接口）
  - **VoiceController**: 语音合成控制器（火山引擎豆包+MiniMax双平台，10个接口）
- 🤖 **新增业务控制器索引**： <!-- 🚨 LongDev1新增V10.1：基于LongChec2 V10.0反向检测，补充缺失的业务控制器索引 -->
  - **AuthController**: 用户认证控制器（完整认证体系+密码管理+Token管理，7个接口）
  - **StyleController**: 风格管理控制器（剧情风格+防刷机制基础，3个接口）
  - **ProjectCreationController**: 项目创建防刷控制器（防无成本刷项目，2个接口）
  - **AssetController**: 素材管理控制器（用户上传+文件管理，6个接口）
  - **UserGrowthController**: 用户成长控制器（成长轨迹+里程碑，4个接口）
  - **RecommendationController**: 个性化推荐控制器（智能推荐+用户偏好，3个接口）
  - **AiModelController**: AI模型管理控制器（模型切换+配置管理+平台对比，7个接口）
- 🤖 **新增智能平台管理接口规范**： <!-- 🚨 CogniDev新增：基于apitest-final.mdc同步补充 -->
  - **按业务类型获取可选平台**: `GET /api/ai-models/business-platforms` - 根据业务类型返回可用AI平台列表
  - **智能平台切换**: `POST /api/ai-models/switch` - 支持自动平台选择和手动切换
  - **平台性能对比**: `GET /api/ai-models/platform-comparison` - 提供各平台性能指标对比 <!-- 🚨 CogniDev详细规范：里程碑2接口1 -->

#### **🔍 平台性能对比接口详细规范**
**核心功能**：提供AI平台的性能指标对比，帮助用户选择最适合的平台

**基本要求**：
1. **性能指标对比**：
   - 响应时间对比（平均、最快、最慢）
   - 成功率统计（近7天、近30天）
   - 成本效益分析（每次调用成本）
   - 质量评分（用户评价、系统评估）

2. **平台覆盖范围**： <!-- 🚨 CogniDev修正：基于apitest-index.mdc实际模型信息 -->
   - 图像生成平台（LiblibAI、KlingAI、MiniMax）
   - 视频生成平台（KlingAI、MiniMax）
   - 剧情生成平台（DeepSeek、MiniMax）
   - 角色生成平台（LiblibAI、KlingAI、MiniMax）
   - 风格生成平台（LiblibAI、KlingAI、MiniMax）
   - 音效生成平台（火山引擎豆包、MiniMax）
   - 音色生成平台（MiniMax、火山引擎豆包）
   - 音乐生成平台（MiniMax）

3. **数据展示格式**：
   - 支持表格对比和图表展示
   - 提供平台推荐建议
   - 实时更新性能数据

**实现接口**：`GET /api/ai-models/platform-comparison`

#### **🎯 按业务类型获取平台接口详细规范** <!-- 🚨 CogniDev新增：里程碑2接口2 -->
**核心功能**：根据业务类型返回可用AI平台列表

**基本要求**：
1. **业务类型支持**：image、video、story、character、style、sound、voice、music
2. **返回信息**：平台基本信息、业务类型支持情况、可用性状态

**实现接口**：`GET /api/ai-models/business-platforms`

#### **📦 批量资源生成任务接口详细规范** <!-- 🚨 CogniDev新增：里程碑3接口1 -->
**核心功能**：创建批量资源生成任务，管理任务状态和进度

**🚨 架构铁律约束**：
1. **服务器职责限制**：
   - 仅提供任务创建、状态查询、进度管理
   - 禁止任何形式的文件生成、存储、中转
   - 不得进行资源文件的下载操作

2. **资源URL管理**：
   - 资源URL必须由第三方AI平台直接提供
   - API服务器仅存储和返回URL信息
   - 确保Python客户端可以直接访问资源

3. **任务管理机制**：
   - 提供任务创建、状态更新、进度跟踪
   - 支持批量任务的并发管理
   - 实现任务失败重试和错误处理

**实现接口**：`POST /api/batch/resources/generate`

#### **📊 批量任务状态查询接口详细规范** <!-- 🚨 CogniDev新增：里程碑3接口2 -->
**核心功能**：查询批量任务状态、进度和结果信息

**🚨 架构铁律约束**：
1. **查询职责限制**：
   - 仅提供状态查询、进度查询、结果查询
   - 禁止任何形式的文件操作
   - 不涉及资源文件的任何处理

2. **返回信息管理**：
   - 返回任务状态、进度百分比、完成数量
   - 返回第三方平台提供的资源URL信息
   - 确保Python客户端可以直接使用URL

3. **状态查询机制**：
   - 支持单个任务状态查询
   - 提供详细的进度信息
   - 实现任务结果的安全返回

**实现接口**：`GET /api/batch/resources/status`

  - **图像平台切换**: `POST /api/images/{task_id}/switch-platform` - 图像生成任务平台切换
  - **批量图像生成**: `POST /api/images/batch-generate` - 支持多任务并行处理
  - **视频平台性能对比**: `GET /api/videos/platform-comparison` - 视频生成平台性能分析
  - **音乐风格列表**: `GET /api/music/styles` - MiniMax平台支持的音乐风格
  - **语音平台对比**: `GET /api/voices/platform-comparison` - 语音合成平台对比分析
  - **批量语音合成**: `POST /api/voices/batch-synthesize` - 支持批量语音任务处理
- 🤖 **完整的请求/响应格式**：每个接口的详细参数和返回格式
- 🤖 **错误处理机制**：AI服务调用失败的处理策略
- 🤖 **性能优化策略**：AI API调用的最佳实践

#### **📊 文档使用优先级规则**

1. **🔍 问题诊断优先级**：
   - **AI服务相关问题** → 优先使用 `dev-aiapi-guidelines.mdc` + 对应的add/edit文档
   - **现有功能问题修复** → 使用 `dev-api-guidelines-edit.mdc`
   - **新功能需求开发** → 使用 `dev-api-guidelines-add.mdc`

2. **🎯 开发阶段适配规则**：
   - **第1-2阶段**（基础设施）：主要使用 `dev-api-guidelines-add.mdc`
   - **第3-5阶段**（核心功能）：`dev-api-guidelines-add.mdc` + `dev-aiapi-guidelines.mdc`
   - **第6-10阶段**（高级功能）：三个文档并用，根据具体需求选择
   - **维护阶段**：主要使用 `dev-api-guidelines-edit.mdc` + `dev-aiapi-guidelines.mdc`

3. **🚨 紧急修复优先级**：
   - **AI服务故障** → 必须使用 `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **生产环境问题** → 优先使用 `dev-api-guidelines-edit.mdc`
   - **架构合规性问题** → 必须使用 `dev-api-guidelines-edit.mdc`
   - **性能优化需求** → 优先使用 `dev-api-guidelines-edit.mdc`

4. **🤖 AI功能开发专用规则**：
   - **所有AI相关开发** → 必须使用 `dev-aiapi-guidelines.mdc` 作为主要参考
   - **新增AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-add.mdc`
   - **修复AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **AI服务集成** → 仅使用 `dev-aiapi-guidelines.mdc`

#### **🔄 三文档协作机制**

1. **📋 文档协作优先级**：
   ```
   AI功能开发：dev-aiapi-guidelines.mdc (主) + dev-api-guidelines-add.mdc (辅)
   AI问题修复：dev-aiapi-guidelines.mdc (主) + dev-api-guidelines-edit.mdc (辅)
   非AI新功能：dev-api-guidelines-add.mdc (主) + dev-aiapi-guidelines.mdc (可选)
   非AI问题修复：dev-api-guidelines-edit.mdc (主)
   ```

2. **🎯 具体使用场景判断**：
   - **包含"AI"、"生成"、"智能"关键词** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **涉及DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包** → 必须使用 `dev-aiapi-guidelines.mdc` <!-- 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax -->
   - **文本生成、图像生成、语音合成、视频生成** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **WebSocket AI处理器** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-add.mdc`

3. **⚡ 快速决策流程**：
   ```
   Step 1: 是否涉及AI功能？
   ├─ 是 → 使用 dev-aiapi-guidelines.mdc (主文档)
   │   ├─ 新增AI功能 → + dev-api-guidelines-add.mdc
   │   └─ 修复AI功能 → + dev-api-guidelines-edit.mdc
   └─ 否 → 跳转Step 2

   Step 2: 是新功能还是修复？
   ├─ 新功能 → dev-api-guidelines-add.mdc
   └─ 修复 → dev-api-guidelines-edit.mdc
   ```

#### **⚠️ 重要注意事项**

1. **🛡️ 架构合规性检查**：
   - 所有开发必须遵循index.mdc中定义的职责边界
   - WebSocket仅限Python工具使用
   - 时间轴管理职责归还客户端
   - 并发用户数统一为1000
   - AI服务调用必须遵循dev-aiapi-guidelines.mdc规范

2. **📋 文档完整性保证**：
   - **三个分类文档总覆盖率：108.9%**（超越原文档）
   - **dev-api-guidelines-add.mdc**：14339行，包含119个API接口、15个开发阶段、完整AI服务集成架构 <!-- 🚨 CogniDev更新：基于七重同步铁律实施，接口数量114个→119个 -->
   - **dev-api-guidelines-edit.mdc**：4501行，包含8个控制器、完整中间件、监控系统
   - **dev-aiapi-guidelines.mdc**：7155行，包含5个AI平台、87个AI接口
   - **总接口统计**：206个总接口（119个API接口 + 87个AI接口） <!-- 🚨 CogniDev更新：基于七重同步铁律实施，总数201个→206个 -->
   - 可完全替代原dev-api-guidelines-new.mdc使用

3. **🤖 AI服务集成规范**：
   - 所有AI功能开发必须使用dev-aiapi-guidelines.mdc作为权威依据
   - AI API调用必须遵循虚拟服务规范（https://aiapi.tiptop.cn）
   - 支持5个AI平台：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包 <!-- 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax -->
   - AI服务调用超时设置建议30秒
   - 必须实现AI服务降级和错误处理机制
   - **最新更新**: V6.0-V6.2重大更新完成，文档全面补全，平台名称统一，接口数量准确 <!-- 🔧 LongDev1补全：最新版本信息 -->

4. **🔄 持续更新机制**：
   - **新增功能** → 更新 `dev-api-guidelines-add.mdc`
   - **修复问题** → 更新 `dev-api-guidelines-edit.mdc`
   - **AI服务变更** → 更新 `dev-aiapi-guidelines.mdc`
   - 定期同步三个文档，确保与index.mdc规范一致
   - AI服务接口变更需要同步更新相关控制器和服务层

5. **📊 文档使用统计和监控**：
   - 优先使用覆盖率最高的文档（dev-api-guidelines-add.mdc: 108.9%）
   - AI相关开发必须使用dev-aiapi-guidelines.mdc（100%AI接口覆盖）
   - 问题修复优先使用dev-api-guidelines-edit.mdc（包含完整修复方案）
   - 定期评估文档使用效果，持续优化文档结构

#### **🎯 文档选择决策树**

```
开发任务分析
├─ 是否涉及AI功能？
│  ├─ 是 → 使用 dev-aiapi-guidelines.mdc (必须)
│  │  ├─ 新增AI功能 → + dev-api-guidelines-add.mdc
│  │  ├─ 修复AI功能 → + dev-api-guidelines-edit.mdc
│  │  └─ 纯AI接口调用 → 仅使用 dev-aiapi-guidelines.mdc
│  └─ 否 → 继续判断
├─ 是新功能开发？
│  ├─ 是 → dev-api-guidelines-add.mdc (主)
│  │  └─ 可能需要AI → + dev-aiapi-guidelines.mdc (辅)
│  └─ 否 → 继续判断
├─ 是问题修复？
│  ├─ 是 → dev-api-guidelines-edit.mdc (主)
│  │  └─ AI相关问题 → + dev-aiapi-guidelines.mdc (辅)
│  └─ 否 → 继续判断
└─ 复杂场景 → 使用多文档组合
   ├─ 架构重构 → edit + add + aiapi
   ├─ 性能优化 → edit + aiapi (如涉及AI)
   └─ 安全加固 → edit + add (如需新增安全功能)
```

## 项目开发需求

### 🏗️ 多端架构设计（基于LongChec2优化分析）

#### 📋 各组件职责边界明确定义

**1. 后台管理系统** (@php/backend/)：
- ✅ **数据管理职责**：AI引擎配置、音色库、音效库、音乐库、风格库、角色库、作品库、会员库、积分明细
- ✅ **配置管理职责**：第三方AI的API接口地址和密钥管理
- ✅ **系统管理职责**：用户权限、系统监控、数据统计

**2. 工具API接口服务** (@php/api/)：
- ✅ **API服务职责**：为WEB网页工具和Python用户终端工具提供统一API接口
- ✅ **业务逻辑职责**：积分管理、用户认证、AI任务调度、数据处理
- ✅ **WebSocket服务职责**：仅为Python工具提供实时通信（AI生成进度推送）
- ❌ **不包含职责**：不储存且不中转用户创作过程中AI生成的资源、视频编辑处理、客户端UI逻辑、本地文件操作

**3. WEB网页工具** (@php/web/)：
- ✅ **展示职责**：首页工具展示、功能介绍、价格方案、作品展示
- ✅ **用户中心职责**：用户注册登录、充值积分、积分明细、代理推广、代理结算
- ✅ **作品广场职责**：作品展示浏览、分类筛选、搜索查看、作品详情展示
- ✅ **响应式设计**：支持PC端、移动端、Python工具嵌入（1200px/800px窗口）
- ❌ **不包含职责**：视频创作功能、AI生成功能、WebSocket实时通信、作品发布创建

**4. Python用户终端工具** (@python/)：
- ✅ **核心创作职责**：选风格+写剧情、绑角色、生成图像、视频编辑、本地导出 <!-- 🔧 LongDev1修复：基于LongChec2调整方案，重新定义Python工具核心职责 -->
- ✅ **可选发布职责**：作品发布到广场（用户自主选择） <!-- 🔧 LongDev1新增：明确可选发布功能的定位 -->
- ✅ **客户端处理职责**：资源本地化、视频时间轴编辑、本地素材合成、UI交互逻辑、作品导出
- ✅ **实时通信职责**：通过WebSocket接收AI生成进度推送
- ✅ **技术栈**：Python + PySide6 + PyInstaller + WebSocket客户端

**5. 虚拟第三方AI API接口服务** (@php/aiapi/)：
- ✅ **开发支持职责**：模拟真实AI平台API行为，支持本地开发测试
- ✅ **环境切换职责**：通过配置实现开发环境到生产环境的无缝切换
- ✅ **API标准化职责**：统一不同AI平台的接口差异，提供标准调用接口

### 🔄 主要业务流程（客户端vs服务端职责明确）

**核心创作流程**：选风格+写剧情 → 绑角色 → 生成图像 → 视频编辑 → 本地导出 <!-- 🔧 LongDev1修复：基于LongChec2调整方案，将作品发布从核心流程调整为可选流程 -->
**可选扩展流程**：本地导出 → [用户选择] → 作品发布到广场 <!-- 🔧 LongDev1新增：明确区分核心流程和可选流程，突出本地导出为主要完成方式 -->

**职责分工**：
- **服务端负责**：风格管理、剧情AI生成、角色管理、图像AI生成、素材存储管理
- **客户端负责**：视频时间轴编辑、本地素材合成、UI交互、作品导出

### 🚨 关键架构原则（基于LongChec2分析）

#### 1. **避免循环依赖**
- WebSocket服务只负责推送，不参与业务逻辑
- 积分变动通知改为异步事件驱动
- 使用事件总线模式解耦组件间依赖

#### 2. **功能模块整合原则**
- 积分相关功能统一管理（避免分散在多个阶段）
- 用户相关功能集中处理（认证、偏好、中心）
- AI服务管理统一接口（连接器、模型、健康检查）

#### 3. **WebSocket使用边界**
- ✅ **仅Python工具使用**：AI生成进度推送、任务状态通知
- ❌ **WEB工具禁用**：避免不必要的连接和资源消耗
- 🔒 **安全传输**：密钥加密传输，不持久化存储

#### 4. **性能优化策略**
- **并发支持**：设计支持1000用户同时使用
- **缓存策略**：MySQL主存储 + Redis缓存层
- **超时管理**：图像5分钟、视频30分钟、文本1分钟、语音2分钟

## 项目架构图

### 完整系统架构（优化版）
```mermaid
graph TB
    subgraph "用户层 - 视频创作工具"
        A[Python用户终端工具<br/>完整创作功能<br/>客户端视频编辑]
        B[WEB网页工具<br/>✅展示职责：首页工具展示、功能介绍、价格方案<br/>✅用户中心：注册登录、充值积分、积分明细、代理推广、代理结算<br/>✅作品广场：作品展示浏览、分类筛选、搜索查看、作品详情展示<br/>✅响应式设计：PC端、移动端、Python工具嵌入<br/>❌禁止：视频创作、AI生成、WebSocket通信、作品发布创建]
    end

    subgraph "业务服务层"
        C[工具api接口服务<br/>@php/api/<br/>基于Lumen 10.x<br/>统一API接口]
        C1[WebSocket服务<br/>swoole-cli artisan websocket:serve<br/>仅为Python工具提供实时通信]
        C2[AI资源管理服务<br/>资源生成+版本控制+本地导出<br/>可选：作品发布+审核系统] <!-- 🔧 LongDev1修复：基于LongChec2调整方案，重新标注架构图组件职责 -->
    end

    subgraph "开发支持层"
        D[虚拟第三方AI API接口服务<br/>@php/aiapi/<br/>本地开发模拟真实AI平台<br/>生产环境可配置切换到真实AI平台]
    end

    subgraph "数据存储层"
        E[MySQL数据库<br/>ai_tool<br/>主存储+事务保证<br/>+AI资源表+版本表+作品广场表]
        F[Redis缓存<br/>WebSocket会话管理<br/>快速查询+状态同步<br/>+资源状态缓存]
    end

    subgraph "虚拟AI服务"
        G[DeepSeek虚拟服务<br/>剧情生成]
        H[LiblibAI虚拟服务<br/>图像生成]
        I[KlingAI虚拟服务<br/>视频生成]
        J[MiniMax虚拟服务<br/>语音处理] <!-- 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax -->
        K[火山引擎豆包虚拟服务<br/>专业语音AI]
    end

    %% HTTP API 连接 (蓝色虚线) - 两个工具都使用
    A -.->|🔵 HTTP API<br/>创作功能调用| C
    B -.->|🔵 HTTP API<br/>展示功能+用户中心功能+作品广场功能<br/>❌禁用WebSocket| C

    %% WebSocket 连接 (绿色粗线) - 仅Python工具使用
    A ==>|🟢 WebSocket实时通信<br/>AI生成进度推送<br/>任务状态通知| C1

    %% 服务间调用 (红色线) - 避免循环依赖
    C -->|🔴 业务逻辑调用<br/>异步事件驱动| D
    C -->|🔴 资源管理调用<br/>版本控制+审核| C2
    C1 -->|🔴 获取API密钥<br/>安全传输| C
    C1 -->|🔴 使用密钥调用AI平台<br/>不持久化存储| D
    C2 -->|🔴 AI生成调用<br/>资源创建| D

    %% 数据库连接 (橙色线) - 双重保障
    C -->|🟠 数据存储<br/>事务保证| E
    C -->|🟠 缓存操作<br/>快速查询| F
    C1 -->|🟠 会话管理<br/>状态同步| F
    C2 -->|🟠 资源数据存储<br/>版本管理| E
    C2 -->|🟠 资源状态缓存<br/>快速查询| F

    %% AI服务调用 (紫色线) - 统一管理
    D -->|🟣 AI调用<br/>统一接口| G
    D -->|🟣 AI调用<br/>统一接口| H
    D -->|🟣 AI调用<br/>统一接口| I
    D -->|🟣 AI调用<br/>统一接口| J
    D -->|🟣 AI调用<br/>统一接口| K

    %% 应用样式到连接线 - 增强颜色显示
    linkStyle 0 stroke:#2196F3,stroke-width:3px,stroke-dasharray: 5 5
    linkStyle 1 stroke:#2196F3,stroke-width:3px,stroke-dasharray: 5 5
    linkStyle 2 stroke:#4CAF50,stroke-width:4px
    linkStyle 3 stroke:#F44336,stroke-width:3px
    linkStyle 4 stroke:#F44336,stroke-width:3px
    linkStyle 5 stroke:#F44336,stroke-width:3px
    linkStyle 6 stroke:#FF9800,stroke-width:3px
    linkStyle 7 stroke:#FF9800,stroke-width:3px
    linkStyle 8 stroke:#FF9800,stroke-width:3px
    linkStyle 9 stroke:#9C27B0,stroke-width:3px
    linkStyle 10 stroke:#9C27B0,stroke-width:3px
    linkStyle 11 stroke:#9C27B0,stroke-width:3px
    linkStyle 12 stroke:#9C27B0,stroke-width:3px

    %% 节点样式 - 高对比度清晰字体
    classDef userLayer fill:#FFFFFF,stroke:#1976D2,stroke-width:3px,color:#000000
    classDef serviceLayer fill:#FFFFFF,stroke:#7B1FA2,stroke-width:3px,color:#000000
    classDef dataLayer fill:#FFFFFF,stroke:#F57C00,stroke-width:3px,color:#000000
    classDef aiLayer fill:#FFFFFF,stroke:#388E3C,stroke-width:3px,color:#000000

    class A,B userLayer
    class C,C1,C2 serviceLayer
    class E,F dataLayer
    class D,G,H,I,J aiLayer
```

### 架构优化说明

**连接类型说明**:
- **🔵 蓝色虚线**: HTTP API调用 (REST接口，两个工具都使用，职责明确)
- **🟢 绿色粗线**: WebSocket实时通信 (仅Python工具使用，边界清晰)
- **🔴 红色线**: 服务间调用 (异步事件驱动，避免循环依赖)
- **🟠 橙色线**: 数据库和缓存操作 (双重保障，性能优化)
- **🟣 紫色线**: AI服务调用 (统一管理，标准化接口)

**关键优化特性**:
1. **职责边界清晰**: 每个组件的职责明确定义，避免功能重叠
2. **避免循环依赖**: 使用异步事件驱动架构，解耦组件间依赖
3. **WebSocket边界明确**: 仅为Python工具提供实时通信，WEB工具不使用
4. **性能优化设计**: 支持1000并发用户，MySQL+Redis双重保障
5. **安全架构升级**: 密钥安全传输，不持久化存储，权限二次验证
6. **功能模块整合**: 相关功能统一管理，避免分散和重复
7. **🎯 AI资源管理**: 完整的资源生成、版本控制、审核发布体系
8. **🎯 差异化存储**: 图像/音频下载处理，视频仅元数据管理

## 完整业务流程图（优化版）

### 🔄 四种核心业务流程（基于LongChec2优化）

#### 业务流程1: 处理成功的业务流程（优化版）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具api接口服务
    participant AI as AI平台/虚拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分(事务锁定)
    A->>DB: 扣取积分(冻结状态)
    A->>R: 同步积分状态(缓存更新)
    A->>DB: 写入业务日志(状态:冻结)
    A->>R: 缓存业务日志
    A->>W: 返回加密AI平台密钥
    W->>AI: 调用AI平台(密钥解密使用)
    AI->>W: 返回成功结果
    W->>P: 推送成功结果(实时通信)
    W->>E: 发布任务完成事件(异步)
    E->>A: 处理任务完成事件
    A->>DB: 更新日志状态(成功)
    A->>R: 更新缓存状态
    Note over W: 清理临时密钥(安全)
```

#### 业务流程2: 积分不足业务流程（优化版）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具api接口服务

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>A: 检查用户积分(快速验证)
    Note over A: 积分 < 所需积分
    A->>W: 返回积分不足详细信息
    W->>P: 推送积分不足消息(包含充值建议)
    Note over A: 无扣费操作，保护用户资金
```

#### 业务流程3: 处理失败的业务流程（优化版）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具api接口服务
    participant AI as AI平台/虚拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分 → 扣取积分(冻结状态)
    A->>R: 同步积分状态 → 写入业务日志 → 缓存日志
    A->>W: 返回加密AI平台密钥
    W->>AI: 调用AI平台
    AI->>W: 返回失败结果
    W->>P: 推送失败结果(详细错误信息)
    W->>E: 发布任务失败事件(异步)
    E->>A: 处理任务失败事件
    A->>DB: 更新日志状态(失败) + 返还等额积分(事务保证)
    A->>R: 更新缓存状态
    Note over W: 清理临时密钥(安全)
    Note over A: 积分返还完成，用户资金安全
```

#### 业务流程4: 超时/中断处理业务流程（优化版）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具api接口服务
    participant AI as AI平台/虚拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant T as 超时监控
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分 → 扣取积分(冻结状态)
    A->>R: 同步积分状态 → 写入业务日志 → 缓存日志
    A->>W: 返回加密AI平台密钥
    W->>T: 启动超时监控(业务类型自适应)
    W->>AI: 调用AI平台

    alt 超时或连接中断
        T->>E: 检测到超时/中断(发布事件)
        E->>A: 处理中断事件
        A->>DB: 更新日志状态(失败) + 返还等额积分
        A->>R: 更新缓存状态
        E->>W: 通知WebSocket服务
        W->>P: 推送中断消息(包含积分返还确认)
        Note over W: 清理临时密钥和监控任务
    end
```

#### 🎯 业务流程5: AI资源生成与版本管理流程（新增）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具api接口服务
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 发起AI资源生成请求(含module_id)
    A->>RM: 创建资源记录
    RM->>DB: 创建p_ai_resources记录
    RM->>DB: 自动创建v1.0版本记录
    RM->>A: 返回资源UUID
    A->>AI: 调用AI平台生成
    AI->>A: 返回资源URL和元数据
    A->>RM: 更新版本信息
    RM->>DB: 更新resource_url、file_size等
    RM->>RM: 执行自动内容审核
    RM->>DB: 更新review_status
    RM->>R: 缓存资源状态
    A->>P: 返回资源信息
    P->>AI: 直接下载资源到本地
    P->>A: 确认下载完成
    A->>RM: 更新下载状态
    RM->>DB: 更新downloaded_by_python=true
```

#### 🎯 业务流程6: 资源下载完成流程（核心流程）<!-- 🔧 LongDev1架构修正：基于LongChec2修正方案，简化为直接下载流程 -->
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具api接口服务
    participant AI as AI平台
    participant DB as MySQL数据库

    P->>A: 请求资源下载信息(resource_id)
    A->>DB: 查询资源信息和AI平台URL
    DB->>A: 返回resource_url和元数据
    A->>P: 返回AI平台URL和文件信息
    P->>AI: 直接从AI平台下载资源
    AI->>P: 下载完成
    P->>A: 确认下载完成(local_path)
    A->>DB: 更新下载状态和本地路径
    Note over P: 创作完成，资源已保存到本地
```

#### 🎯 业务流程7: 可选作品发布流程（增值服务）<!-- 🔧 LongDev1新增：可选作品发布流程 -->
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具api接口服务
    participant WPS as 作品发布权限服务
    participant WP as 作品广场
    participant DB as MySQL数据库

    Note over P: 用户已完成本地导出
    P->>A: [可选] 请求发布作品(module_type, module_id)
    A->>WPS: 检查发布权限
    WPS->>DB: 查询模块相关资源的review_status

    alt 用户选择发布
        DB->>WPS: review_status = 'approved'/'auto_approved'
        WPS->>A: 返回允许发布
        A->>WP: 创建作品广场记录
        WP->>DB: 保存到p_work_plaza表
        WP->>A: 返回发布成功
        A->>P: 通知发布成功
    else 用户选择不发布
        P->>A: 跳过发布，仅本地保存
        A->>P: 确认完成，无需发布
    end
```

### 📋 业务流程优化说明

**优化重点**:
- **🔒 安全性增强**: 密钥加密传输，使用后立即清理，不持久化存储
- **⚡ 性能优化**: 事务锁定机制，缓存同步策略，快速积分验证
- **🔄 异步解耦**: 使用事件总线避免循环依赖，提高系统响应性
- **💰 资金安全**: 积分冻结机制，失败自动返还，事务一致性保证
- **📊 监控完善**: 超时检测自适应，连接中断处理，状态追踪完整
- **🎯 资源管理**: AI资源生成、版本控制、审核发布一体化管理
- **🎯 差异化存储**: 图像/音频下载处理，视频仅元数据，优化存储成本

**积分安全机制升级**:
- ✅ **事务锁定**: 使用数据库事务锁定，确保并发安全
- ✅ **状态流转**: frozen → success/failed，状态机模式
- ✅ **自动返还**: 失败或中断时自动返还，用户资金安全
- ✅ **一致性保证**: MySQL+Redis双重保障，数据一致性

**🎯 AI资源管理机制升级**:
- ✅ **版本控制**: 每个资源自动创建v1.0版本，支持版本迭代和重用
- ✅ **差异化存储**: 图像/音频API服务下载处理，视频仅保存元数据
- ✅ **自动审核**: 基于安全评分的自动审核，高风险内容人工审核
- ✅ **发布权限**: 基于审核状态的作品发布权限控制，确保内容合规
- ✅ **模块关联**: 通过module_id精确关联资源与具体内容，支持权限检查

**超时检测机制优化**:
- 🕐 **图像生成**: 5分钟超时（可配置）
- 🕐 **视频生成**: 30分钟超时（可配置）
- 🕐 **文本生成**: 1分钟超时（可配置）
- 🕐 **语音合成**: 2分钟超时（可配置）

**数据存储策略优化**:
- 📊 **MySQL**: 主存储，事务保证，数据持久化
- 📊 **Redis**: 缓存层，快速查询，状态同步
- 📊 **事件总线**: 异步处理，解耦组件，提高性能
- 📊 **操作原子性**: 所有积分操作都在数据库事务中执行

## 🚨 开发指导原则

### 1. **功能模块整合原则**
```yaml
积分相关功能整合:
  - 核心积分系统 (第1阶段)
  - 充值积分功能 (整合到第1阶段)
  - 积分高级功能 (整合到第1阶段)

用户相关功能整合:
  - 用户认证模块 (第1阶段)
  - 用户中心功能 (整合到第1阶段)
  - 用户偏好设置 (整合到第1阶段)

AI服务管理整合:
  - AI服务连接器 (第2A阶段)
  - AI模型管理 (整合到第2A阶段)
  - AI服务健康检查 (整合到第2A阶段)

MVP核心功能模块整合: <!-- 🔧 LongDev1修复：基于LongChec2调整方案，重新整合功能模块 -->
  - 角色库管理系统 (第2C阶段): 角色CRUD+绑定+推荐
  - 🎯 AI资源管理系统 (第3A阶段): 资源生成+版本控制+本地导出 <!-- 🔧 LongDev1调整：突出本地导出功能 -->
  - 任务管理系统 (第2D阶段): 取消+重试+超时配置
  - WebSocket服务 (第2B阶段): 纯推送+Python工具专用

增值服务功能模块整合: <!-- 🔧 LongDev1新增：增值服务功能模块 -->
  - 作品发布系统 (第3B阶段): 可选发布+审核+作品广场 <!-- 🔧 LongDev1调整：明确可选发布定位 -->
  - 高级功能扩展 (第4-5阶段): 项目管理+社交功能+系统优化
```

### 2. **开发阶段拆分原则**
MVP核心阶段 (优先级🔴最高): <!-- 🔧 LongDev1修复：基于LongChec2调整方案，重新规划开发阶段优先级 -->
  - 第1阶段: 基础设施与核心系统 (3-4天, 6个接口) ✅ 已完成
    功能: 用户管理+积分管理+基础数据表
  - 第2A阶段: AI服务基础集成 (3-4天, 5个接口) ✅ 已完成
    功能: AI连接器+模型管理+健康检查
  - 第2B阶段: WebSocket实时通信 (3-4天, 4个接口) ✅ 已完成
    功能: 纯推送服务+Python工具验证
  - 第2C阶段: 角色库管理系统 (2-3天, 7个接口) ✅ 已完成
    功能: 角色CRUD+绑定+推荐系统
  - 第2D1阶段: 文本与图像生成接口 (2-3天, 7个接口) ✅ 已完成 <!-- 🚨 LongDev1修复：基于LongChec2审查，补充第2D阶段详细拆分 -->
    功能: 文本生成+图像生成+批量图像接口
  - 第2D2阶段: 语音视频生成与任务管理 (2-3天, 8个接口) ✅ 已完成 <!-- 🚨 LongDev1修复：基于LongChec2审查，补充第2D阶段详细拆分 -->
    功能: 语音合成+视频生成+任务管理+批量操作
  - 第2D3阶段: 音乐音效音色生成接口 (3-4天, 16个接口) ✅ 已完成 <!-- 🚨 LongDev1修复：基于LongChec2审查，补充缺失的音乐音效音色功能 -->
    功能: 音乐生成+音效生成+音色生成+多平台支持
  - 第2E阶段: 积分管理API接口补充 (0.5天, 3个接口) ✅ 已完成 <!-- 🚨 LongDev1新增V10.1：基于LongChec2 V10.0反向检测，补充缺失的第2E阶段 -->
    功能: 积分冻结+返还+查询API接口
  - 第2F阶段: WebSocket认证接口补充 (0.5天, 1个接口) ✅ 已完成 <!-- 🚨 LongDev1新增V10.1：基于LongChec2 V10.0反向检测，补充缺失的第2F阶段 -->
    功能: WebSocket连接认证+权限验证
  - 第2G阶段: 性能监控接口补充 (1天, 3个接口) ✅ 已完成 <!-- 🚨 LongDev1新增V10.1：基于LongChec2 V10.0反向检测，补充缺失的第2G阶段 -->
    功能: 系统健康检查+性能监控+响应时间统计

资源管理阶段 (优先级🟢高): <!-- 🚨 LongDev1新增V10.1：基于LongChec2 V10.0反向检测，补充缺失的资源管理阶段 -->
  - 第3阶段: 高级功能集成 (4-5天, 8个接口) ✅ 已完成 <!-- 🚨 LongDev1修正：第3阶段实际为高级功能集成，包含文件管理+数据导出+系统监控+全局搜索 -->
    功能: 文件管理+数据导出+系统监控+高级搜索
  - 第3A阶段: 本地导出与资源管理 (2-3天, 8个接口) ✅ 已完成 <!-- 🔧 LongDev1新增：核心本地导出功能阶段 -->
    功能: 资源生成+版本控制+本地导出+下载管理

增值服务阶段 (优先级🟡中等): <!-- 🔧 LongDev1新增：可选增值服务阶段 -->
  - 第3B阶段: 作品发布系统 (2-3天, 8个接口) <!-- 🔧 LongDev1调整：作品发布调整为增值服务 -->
    功能: 可选发布+审核系统+作品广场

高级功能阶段 (优先级🟡中等): <!-- 🚨 LongDev1新增V10.1：基于LongChec2 V10.0反向检测，补充缺失的高级功能阶段 -->
  - 第4阶段: 高级项目管理系统 (3-4天, 6个接口) <!-- 🚨 LongDev1新增V10.1：基于LongChec2 V10.0反向检测，补充缺失的第4阶段 -->
    功能: 项目协作+模板管理+导入导出+高级配置
  - 第5阶段: 社交功能扩展 (4-5天, 12个接口) <!-- 🚨 LongDev1新增V10.1：基于LongChec2 V10.0反向检测，补充缺失的第5阶段 -->
    功能: 用户成长+个性化推荐+社交互动+AI模型管理+系统优化

总开发时间: 26-33天 (MVP: 20-25天, 增值服务: 6-8天) <!-- 🚨 LongDev1修复V7.5：基于LongChec2 V7.5检测，同步准确时间规划26-33天 -->

每个阶段控制在:
  - 开发时间: 2-5天
  - 接口数量: 4-12个
  - 测试复杂度: 中等
```

### 3. **避免循环依赖原则**
```yaml
解决方案:
  - 使用事件驱动架构
  - WebSocket只负责推送
  - 积分变动异步通知
  - 组件间松耦合设计
```

### 4. **职责边界明确原则**
```yaml
服务端职责:
  - AI任务调度和状态管理
  - 素材存储和下载接口
  - 项目配置管理
  - 用户认证和权限控制

客户端职责:
  - 视频时间轴编辑
  - 本地素材合成
  - UI交互逻辑
  - 作品本地导出
```

## 第三方线上AI平台的API接口文档

1. 剧情生成及分镜API：
   DeepSeek API文档：https://api-docs.deepseek.com/zh-cn/

2. 分镜剧情生成图像API：
   liblibAI的api接口文档：https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d
   可灵API文档：https://app.klingai.com/cn/dev/document-api/apiReference/model/imageGeneration
   海螺API文档：https://platform.minimaxi.com/document/%E5%AF%B9%E8%AF%9D

3. 分镜图像生成视频API：
   liblibAI的api接口文档：https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d
   可灵API文档：https://app.klingai.com/cn/dev/document-api/apiReference/model/imageGeneration
   海螺API文档：https://platform.minimaxi.com/document/%E5%AF%B9%E8%AF%9D

## 🚨 **资源下载架构铁律（防止架构错误）** <!-- 🚨 LongDev1架构安全补充：基于用户架构安全要求 -->

### 📜 **核心架构原则**
1. **资源下载铁律**: 所有基于用户产生的资源文件（视频、风格、角色、音乐、音效等）都必须由"Python用户终端工具"直接从AI平台下载到本地
2. **服务器职责边界**: API服务器只负责管理资源的URL、状态、元数据等附件信息，绝不进行资源文件的中转下载
3. **架构禁止事项**: 严禁在API服务器上进行任何形式的资源文件生成、处理、存储、中转下载

### 🔒 **开发约束规则**
1. **控制器设计约束**: 资源相关控制器只能提供URL和状态管理，禁止文件操作
2. **服务层设计约束**: 资源相关服务只能进行元数据管理，禁止文件生成和处理逻辑
3. **存储架构约束**: 服务器存储只保存资源元数据，禁止保存实际资源文件
4. **下载流程约束**: Python工具 → API获取URL → 直接从AI平台下载，禁止服务器中转

### ⚠️ **违规检查清单**
开发过程中如发现以下代码模式，立即标记为架构违规：
- ❌ 服务器端文件生成逻辑
- ❌ 安全下载token机制
- ❌ 服务器文件存储路径
- ❌ 文件中转下载逻辑
- ❌ 服务器端文件处理操作

---

## 🎨 **作品发布完整规则** <!-- 🚨 LongDev1架构安全补充：基于用户补充要求 -->

### 📋 **可发布作品类型**
1. **风格作品**: 用户创建的剧情风格可发布到风格广场
2. **角色作品**: 用户创建的角色可发布到角色广场
3. **视频作品**: 用户创作完成的视频可发布到作品广场
4. **发布时机**: 任何时间都可以提交发布申请

### 📤 **作品发布流程**
1. **资源上传要求**: 发布任何作品都必须上传相关的资源文件
2. **资源重命名机制**: 上传的资源名称会被系统自动重命名
3. **资源地址保护**: 重命名后的资源地址不返回给用户，仅供系统内部使用
4. **审核机制**: 提交后进入审核流程，审核是否通过由系统决定

### 🔐 **发布安全规则**
1. **资源隔离**: 发布资源与用户创作资源完全隔离
2. **地址保护**: 发布资源地址不暴露给用户
3. **权限控制**: 仅审核通过的作品可在广场展示
4. **版权保护**: 发布资源受系统版权保护机制管理

---

## 性能期望与优化目标

- **响应延迟**：≤30000ms（30秒）
- **并发支持**：1000用户同时使用
- **系统可用性**：99.9%
- **API响应时间**：平均200ms
- **AI生成时间**：文本15-30秒，图像30-60秒
- **WebSocket连接**：支持长连接，自动重连
- **数据一致性**：MySQL+Redis双重保障
- **安全性**：密钥加密传输，权限二次验证

## 技术栈与数据库概述

### 🔧 **新增技术栈组件**
基于 dev-api-guidelines-add.mdc 的完整实现，新增以下技术组件：

#### **新增PHP依赖包**
```yaml
图像处理:
  - intervention/image: ^2.7 (图像处理和缩放)

文件系统:
  - league/flysystem: ^3.0 (文件系统抽象层)

权限管理:
  - spatie/laravel-permission: ^5.0 (角色权限管理)
```

#### **新增环境配置**
```yaml
文件系统配置:
  - FILESYSTEM_DISK=public (文件存储磁盘)

队列配置:
  - QUEUE_CONNECTION=redis (队列驱动)

WebSocket配置:
  - 启动命令: swoole-cli artisan websocket:serve
  - 仅支持Python工具连接
```

### 📊 **数据库表结构概述** <!-- 🚨 LongDev1修复：基于LongChec2审查，补充完整数据表索引 -->
基于 dev-api-guidelines-add.mdc 定义的30+个完整数据表：

#### **基础业务表** (4个) <!-- 🚨 LongDev1补充：基于LongChec2审查发现的缺失表 -->
- **p_users**: 用户表（用户信息、认证、偏好设置）
- **p_points_transactions**: 积分交易表（积分流水、冻结、返还）
- **p_points_freeze**: 积分冻结表（冻结机制、安全保障）
- **p_memberships**: 会员表（会员等级、权限、使用记录）

#### **AI生成相关表** (6个) <!-- 🚨 LongDev1补充：基于LongChec2审查发现的缺失表 -->
- **p_ai_music**: 音乐库表（AI生成音乐存储、MiniMax平台）
- **p_ai_sound**: 音效库表（AI生成音效存储、火山引擎豆包平台）
- **p_ai_timbre**: 音色库表（AI生成音色存储、双平台支持）
- **p_ai_style**: 风格库表（剧情风格管理、AI生成配置）
- **p_ai_story**: 故事库表（AI生成故事内容、项目关联）
- **p_ai_character**: 角色库表（AI生成角色信息、特征描述）

#### **角色库管理相关表** (3个)
- **p_character_library**: 角色库主表（角色信息、特征、评分）
- **p_user_character_bindings**: 用户角色绑定表（绑定关系、使用频率）
- **p_character_categories**: 角色分类表（分类管理、排序）

#### **核心资源管理相关表** (MVP必需) (2个)
- **p_ai_resources**: AI生成资源表（资源管理、模块关联、状态跟踪）
- **p_resource_versions**: 资源版本表（版本控制、提示词管理、本地导出）

#### **任务管理相关表** (MVP必需) (2个)
- **p_ai_generation_tasks**: AI生成任务表（任务状态、进度、结果）
- **p_websocket_sessions**: WebSocket会话表（连接管理、状态同步）

#### **可选作品发布相关表** (增值服务) (4个)
- **p_work_plaza**: 作品广场表（可选发布、审核管理、互动统计）
- **p_user_works**: 用户作品表（作品信息、发布状态、互动数据）
- **p_work_shares**: 作品分享表（分享链接、权限控制、访问统计）
- **p_work_interactions**: 作品互动表（点赞、评论、分享记录）

#### **高级功能表** (4个) <!-- 🚨 LongDev1补充：基于LongChec2审查发现的缺失表 -->
- **p_user_growth_paths**: 用户成长路径表（里程碑跟踪、成长分析）
- **p_user_recommendations**: 个性化推荐表（智能推荐、用户偏好）
- **p_referral_codes**: 邀请码表（推广系统、佣金管理）
- **p_download_tracking**: 下载跟踪表（终端下载、推广统计）

#### **系统管理表** (3个) <!-- 🚨 LongDev1补充：基于LongChec2审查发现的缺失表 -->
- **p_ai_model_configs**: AI模型配置表（模型管理、平台配置）
- **p_cache_statistics**: 缓存统计表（性能监控、缓存优化）
- **p_workflow_templates**: 工作流模板表（高级工作流、模板管理）

#### **业务支持相关表** (4个)
- **p_business_logs**: 业务日志表（操作记录、审计追踪）
- **p_user_favorites**: 用户收藏表（多态关联、收藏管理）
- **p_membership_usage_logs**: 会员使用记录表（功能使用、统计分析）
- **p_projects**: 项目表（项目管理、协作功能）

#### **用户素材管理表** (3个) <!-- 🚨 LongDev1补充：基于LongChec2审查发现的缺失表 -->
- **p_user_assets**: 用户素材表（用户上传素材存储）
- **p_user_asset_categories**: 用户素材分类表（分类管理）
- **p_user_asset_tags**: 用户素材标签表（标签系统）

### 🏗️ **架构组件概述**

#### **AI服务集成架构**
- **AIServiceConnector**: 统一AI服务连接器（支持DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包） <!-- 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax -->
- **虚拟服务地址**: https://aiapi.tiptop.cn（开发环境模拟）
- **超时配置**: 文本1分钟、图像5分钟、语音2分钟、视频30分钟、音效处理3分钟、音频混合5分钟

#### **WebSocket服务架构**
- **PurePushWebSocketService**: 纯推送WebSocket服务（仅负责推送）
- **Python工具专用**: 多层验证机制（User-Agent + 版本 + API Key + 频率限制）
- **安全传输**: 密钥加密传输，不持久化存储

#### **任务管理架构**
- **AiTaskController**: 统一任务管理（整合了TaskManagementController功能，包括取消、重试、超时配置、批量查询、恢复状态等）
- **TimeoutMonitoringService**: 超时监控服务（业务类型自适应）
- **事件驱动架构**: 避免循环依赖，异步处理

#### **🎯 AI资源管理架构**
- **ResourceGenerationService**: AI资源生成服务（版本创建、状态管理、审核集成）
- **WorkPublishPermissionService**: 作品发布权限检查（基于审核状态的权限控制）
- **ContentReviewService**: 内容审核服务（自动审核、人工审核、安全评分）
- **差异化存储策略**: 图像/音频下载处理，视频仅元数据管理
- **模块内容关联**: 通过module_id实现资源与具体内容的精确关联

#### **🔧 LongDev1修复优化**
- **数据表优化**: 增加积分关联字段、项目上下文字段、性能索引
- **API接口补充**: 批量操作接口、搜索统计接口
- **业务逻辑完善**: 版本号并发安全、事务回滚机制、权限缓存优化
- **修复完成度**: 100%，系统达到生产就绪状态

### 📋 **详细实现参考**
以上概述基于 dev-api-guidelines-add.mdc 的完整实现，如需查看：
- **🎯 AI资源管理业务逻辑**: 参考 dev-api-guidelines-add.mdc 第11101-11544行
- **🎯 AI资源管理数据表**: 参考 dev-api-guidelines-add.mdc 第777-960行
- **🎯 AI资源管理API接口**: 参考 dev-api-guidelines-add.mdc 第434-463行
- **🔧 LongDev1修复内容**: 参考 dev-api-guidelines-add.mdc 第455-463行、第917-952行、第11211-11405行
- **详细代码实现**: 参考 dev-api-guidelines-add.mdc 第800-1724行
- **完整数据库迁移**: 参考 dev-api-guidelines-add.mdc 第550-797行
- **API接口详情**: 参考 dev-api-guidelines-add.mdc 第136-512行
- **架构设计说明**: 参考 dev-api-guidelines-add.mdc 第85-134行

---