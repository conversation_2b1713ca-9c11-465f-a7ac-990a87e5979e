<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\StoryService;
use Illuminate\Http\Request;

/**
 * AI故事生成与状态查询
 */
class StoryController extends Controller
{
    protected $storyService;

    public function __construct(StoryService $storyService)
    {
        $this->storyService = $storyService;
    }

    /**
     * @ApiTitle (故事生成)
     * @ApiSummary (使用AI生成故事内容)
     * @ApiMethod (POST)
     * @ApiRoute (/stories/generate)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="prompt", type="string", required=true, description="故事生成提示词")
     * @ApiParams (name="style_id", type="int", required=false, description="风格ID")
     * @ApiParams (name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams (name="length", type="string", required=false, description="故事长度：short/medium/long")
     * @ApiParams (name="genre", type="string", required=false, description="故事类型")
     * @ApiParams (name="platform", type="string", required=false, description="指定AI平台：deepseek/minimax")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.story_content", type="string", required=false, description="生成的故事内容")
     * @ApiReturnParams (name="data.scenes", type="array", required=false, description="分镜场景列表")
     * @ApiReturnParams (name="data.characters", type="array", required=false, description="角色列表")
     * @ApiReturnParams (name="data.cost", type="decimal", required=false, description="消耗的积分")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "故事生成成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "completed",
     *     "story_content": "这是AI生成的故事内容...",
     *     "scenes": [
     *       {
     *         "scene_number": 1,
     *         "description": "场景描述",
     *         "characters": ["角色1", "角色2"],
     *         "duration": "30秒"
     *       }
     *     ],
     *     "characters": [
     *       {
     *         "name": "主角",
     *         "description": "角色描述",
     *         "personality": "性格特点"
     *       }
     *     ],
     *     "cost": "0.0150"
     *   }
     * })
     */
    public function generate(Request $request)
    {
        $rules = [
            'prompt' => 'required|string|min:5|max:2000',
            'style_id' => 'sometimes|integer|exists:style_library,id',
            'project_id' => 'sometimes|integer|exists:projects,id',
            'length' => 'sometimes|string|in:short,medium,long',
            'genre' => 'sometimes|string|max:50',
            'platform' => 'sometimes|string|in:deepseek,minimax'
        ];

        $messages = [
            'prompt.required' => '故事提示词不能为空',
            'prompt.min' => '故事提示词至少5个字符',
            'prompt.max' => '故事提示词不能超过2000个字符',
            'style_id.exists' => '风格不存在',
            'project_id.exists' => '项目不存在',
            'length.in' => '故事长度必须是：short、medium、long之一',
            'platform.in' => 'AI平台必须是：deepseek、minimax之一'
        ];

        try {
            $this->validateData($request->all(), $rules, $messages, []);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        }

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(
                $authResult['response']['message'],
                $authResult['response']['code']
            );
        }

        $user = $authResult['user'];
        
        $generationParams = [
            'length' => $request->get('length', 'medium'),
            'genre' => $request->get('genre'),
            'platform' => $request->get('platform', 'deepseek')
        ];

        $result = $this->storyService->generateStory(
            $user->id,
            $request->prompt,
            $request->style_id,
            $request->project_id,
            $generationParams
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (故事生成状态查询)
     * @ApiSummary (查询故事生成任务的状态和结果)
     * @ApiMethod (GET)
     * @ApiRoute (/stories/{id}/status)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "story_generation",
     *     "status": "completed",
     *     "platform": "deepseek",
     *     "story_content": "生成的故事内容...",
     *     "scenes": [],
     *     "characters": [],
     *     "cost": "0.0150",
     *     "processing_time_ms": 1200,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:01"
     *   }
     * })
     */
    public function getStatus(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(
                $authResult['response']['message'],
                $authResult['response']['code']
            );
        }

        $user = $authResult['user'];
        $result = $this->storyService->getStoryStatus($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
