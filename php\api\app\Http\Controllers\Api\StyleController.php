<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Models\StyleLibrary;
use App\Services\StyleService;
use Illuminate\Http\Request;

/**
 * 剧情风格管理与创作辅助
 */
class StyleController extends Controller
{
    protected $styleService;

    public function __construct(StyleService $styleService)
    {
        $this->styleService = $styleService;
    }

    /**
     * @ApiTitle (获取剧情风格列表)
     * @ApiSummary (获取可用的剧情风格列表)
     * @ApiMethod (GET)
     * @ApiRoute (/styles/list)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="category", type="string", required=false, description="风格分类筛选")
     * @ApiParams (name="is_premium", type="boolean", required=false, description="是否高级风格")
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.styles", type="array", required=true, description="风格列表")
     * @ApiReturnParams (name="data.pagination", type="object", required=true, description="分页信息")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "styles": [
     *       {
     *         "id": 1,
     *         "name": "浪漫爱情",
     *         "description": "温馨浪漫的爱情故事风格",
     *         "category": "romance",
     *         "thumbnail": "https://example.com/thumb.jpg",
     *         "is_premium": false,
     *         "usage_count": 100,
     *         "rating": 4.5
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 50,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function list(Request $request)
    {
        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 20), 100);
        
        $query = StyleLibrary::active()->ordered();
        
        // 分类筛选
        if ($request->has('category')) {
            $query->byCategory($request->category);
        }
        
        // 高级风格筛选
        if ($request->has('is_premium')) {
            if ($request->boolean('is_premium')) {
                $query->premium();
            } else {
                $query->free();
            }
        }
        
        $styles = $query->paginate($perPage, ['*'], 'page', $page);

        return $this->successResponse([
            'styles' => $styles->items(),
            'pagination' => [
                'current_page' => $styles->currentPage(),
                'total' => $styles->total(),
                'per_page' => $styles->perPage(),
                'last_page' => $styles->lastPage()
            ]
        ], 'success');
    }

    /**
     * @ApiTitle (获取风格详情)
     * @ApiSummary (获取指定风格的详细信息)
     * @ApiMethod (GET)
     * @ApiRoute (/styles/{id})
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="风格ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="风格详情")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 1,
     *     "name": "浪漫爱情",
     *     "description": "温馨浪漫的爱情故事风格，适合创作温馨感人的爱情故事",
     *     "category": "romance",
     *     "style_config": {
     *       "tone": "romantic",
     *       "pace": "slow",
     *       "mood": "warm"
     *     },
     *     "prompt_template": "创作一个温馨浪漫的爱情故事...",
     *     "thumbnail": "https://example.com/thumb.jpg",
     *     "is_premium": false,
     *     "usage_count": 100,
     *     "rating": 4.5,
     *     "tags": ["浪漫", "温馨", "爱情"]
     *   }
     * })
     */
    public function detail($id)
    {
        $style = StyleLibrary::active()->find($id);
        
        if (!$style) {
            return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '风格不存在');
        }

        // 增加使用次数
        $style->incrementUsage();

        return $this->successResponse([
            'id' => $style->id,
            'name' => $style->name,
            'description' => $style->description,
            'category' => $style->category,
            'style_config' => $style->style_config ?? [],
            'prompt_template' => $style->prompt_template,
            'thumbnail' => $style->thumbnail,
            'is_premium' => $style->is_premium,
            'usage_count' => $style->usage_count,
            'rating' => $style->rating,
            'tags' => $style->tags ?? []
        ], 'success');
    }

    /**
     * @ApiTitle (获取热门风格)
     * @ApiSummary (获取使用次数最多的热门风格)
     * @ApiMethod (GET)
     * @ApiRoute (/styles/popular)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="limit", type="int", required=false, description="返回数量，默认10")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="array", required=true, description="热门风格列表")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "浪漫爱情",
     *       "thumbnail": "https://example.com/thumb.jpg",
     *       "usage_count": 1000,
     *       "rating": 4.8
     *     }
     *   ]
     * })
     */
    public function popular(Request $request)
    {
        $limit = min($request->get('limit', 10), 50);
        
        $styles = StyleLibrary::active()
            ->popular($limit)
            ->get(['id', 'name', 'thumbnail', 'usage_count', 'rating', 'category']);

        return $this->successResponse($styles->toArray(), 'success');
    }

    /**
     * 创建风格
     * 修复500错误 - 添加缺失的create方法
     *
     * @param Request $request
     * @return array
     */
    public function create(Request $request)
    {
        try {
            // 验证请求参数
            $this->validateData($request->all(), [
                'name' => 'required|string|max:100',
                'description' => 'required|string|max:500',
                'category' => 'sometimes|string|max:50',
                'is_premium' => 'sometimes|boolean'
            ]);

            // 创建风格
            $style = new StyleLibrary();
            $style->name = $request->input('name');
            $style->description = $request->input('description');
            $style->category = $request->input('category', 'custom');
            $style->is_premium = $request->input('is_premium', false);
            $style->is_active = true;
            $style->usage_count = 0;
            $style->rating = 0;
            $style->created_at = \Carbon\Carbon::now();
            $style->updated_at = \Carbon\Carbon::now();
            $style->save();

            return $this->successResponse([
                'id' => $style->id,
                'name' => $style->name,
                'description' => $style->description,
                'category' => $style->category,
                'is_premium' => $style->is_premium
            ], '风格创建成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            return $this->errorResponse(ApiCodeEnum::SYSTEM_ERROR, '创建风格失败: ' . $e->getMessage());
        }
    }
}
