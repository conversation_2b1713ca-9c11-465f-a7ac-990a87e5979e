<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\ReviewService;
use Illuminate\Http\Request;

/**
 * 内容审核与安全管理
 */
class ReviewController extends Controller
{
    protected $reviewService;

    public function __construct(ReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    /**
     * @ApiTitle(提交审核)
     * @ApiSummary(提交作品进行人工审核)
     * @ApiMethod(POST)
     * @ApiRoute(/api/reviews/submit)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="publication_id", type="int", required=true, description="发布ID")
     * @ApiParams(name="review_type", type="string", required=false, description="审核类型：auto/manual/priority")
     * @ApiParams(name="additional_info", type="string", required=false, description="补充说明")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "审核提交成功",
     *   "data": {
     *     "review_id": 123,
     *     "publication_id": 456,
     *     "review_type": "manual",
     *     "status": "pending",
     *     "estimated_time": "2-4小时",
     *     "queue_position": 15,
     *     "submitted_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function submit(Request $request)
    {
        $rules = [
            'publication_id' => 'required|integer|exists:publications,id',
            'review_type' => 'sometimes|string|in:auto,manual,priority',
            'additional_info' => 'sometimes|string|max:500'
        ];

        $messages = [
            'publication_id.required' => '发布ID不能为空',
            'publication_id.exists' => '发布记录不存在',
            'review_type.in' => '审核类型必须是：auto、manual、priority之一',
            'additional_info.max' => '补充说明不能超过500个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $reviewData = [
            'publication_id' => $request->publication_id,
            'review_type' => $request->get('review_type', 'auto'),
            'additional_info' => $request->get('additional_info')
        ];

        $result = $this->reviewService->submitReview($user->id, $reviewData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(获取审核状态)
     * @ApiSummary(查询审核进度和结果)
     * @ApiMethod(GET)
     * @ApiRoute(/api/reviews/{id}/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="审核ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "review_id": 123,
     *     "publication_id": 456,
     *     "status": "approved",
     *     "review_type": "manual",
     *     "reviewer_id": 789,
     *     "reviewer_name": "审核员001",
     *     "review_message": "作品内容健康，质量优秀，符合发布标准",
     *     "review_score": 95,
     *     "review_criteria": {
     *       "content_quality": 9,
     *       "originality": 10,
     *       "compliance": 10,
     *       "technical_quality": 8
     *     },
     *     "submitted_at": "2024-01-01 12:00:00",
     *     "reviewed_at": "2024-01-01 14:30:00",
     *     "processing_time": "2小时30分钟",
     *     "appeal_deadline": "2024-01-08 14:30:00"
     *   }
     * })
     */
    public function getStatus(Request $request, $reviewId)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->reviewService->getReviewStatus($reviewId, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(申请复审)
     * @ApiSummary(对审核结果提出申诉，申请重新审核)
     * @ApiMethod(POST)
     * @ApiRoute(/api/reviews/{id}/appeal)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="审核ID")
     * @ApiParams(name="appeal_reason", type="string", required=true, description="申诉理由")
     * @ApiParams(name="additional_evidence", type="string", required=false, description="补充证据")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "申诉提交成功",
     *   "data": {
     *     "appeal_id": 123,
     *     "review_id": 456,
     *     "status": "pending",
     *     "appeal_reason": "认为审核结果有误",
     *     "estimated_time": "3-5个工作日",
     *     "submitted_at": "2024-01-01 15:00:00"
     *   }
     * })
     */
    public function appeal($reviewId, Request $request)
    {
        $rules = [
            'appeal_reason' => 'required|string|min:10|max:1000',
            'additional_evidence' => 'sometimes|string|max:2000'
        ];

        $messages = [
            'appeal_reason.required' => '申诉理由不能为空',
            'appeal_reason.min' => '申诉理由至少10个字符',
            'appeal_reason.max' => '申诉理由不能超过1000个字符',
            'additional_evidence.max' => '补充证据不能超过2000个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $appealData = [
            'appeal_reason' => $request->appeal_reason,
            'additional_evidence' => $request->get('additional_evidence')
        ];

        $result = $this->reviewService->submitAppeal($reviewId, $user->id, $appealData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(我的审核记录)
     * @ApiSummary(获取用户的审核历史记录)
     * @ApiMethod(GET)
     * @ApiRoute(/api/reviews/my-reviews)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="status", type="string", required=false, description="状态过滤")
     * @ApiParams(name="review_type", type="string", required=false, description="审核类型过滤")
     * @ApiParams(name="date_from", type="string", required=false, description="开始日期")
     * @ApiParams(name="date_to", type="string", required=false, description="结束日期")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "reviews": [
     *       {
     *         "review_id": 123,
     *         "publication_id": 456,
     *         "publication_title": "我的AI故事",
     *         "status": "approved",
     *         "review_type": "manual",
     *         "review_score": 95,
     *         "submitted_at": "2024-01-01 12:00:00",
     *         "reviewed_at": "2024-01-01 14:30:00"
     *       }
     *     ],
     *     "statistics": {
     *       "total_reviews": 15,
     *       "approved_count": 12,
     *       "rejected_count": 2,
     *       "pending_count": 1,
     *       "average_score": 87.5,
     *       "approval_rate": 80.0
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 15,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function myReviews(Request $request)
    {
        $rules = [
            'status' => 'sometimes|string|in:pending,approved,rejected,appealing',
            'review_type' => 'sometimes|string|in:auto,manual,priority',
            'date_from' => 'sometimes|date_format:Y-m-d',
            'date_to' => 'sometimes|date_format:Y-m-d|after_or_equal:date_from',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $filters = [
            'status' => $request->get('status'),
            'review_type' => $request->get('review_type'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to'),
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 20)
        ];

        $result = $this->reviewService->getUserReviews($user->id, $filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(审核队列状态)
     * @ApiSummary(获取当前审核队列的整体状态)
     * @ApiMethod(GET)
     * @ApiRoute(/api/reviews/queue-status)
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "queue_info": {
     *       "total_pending": 156,
     *       "auto_review_pending": 89,
     *       "manual_review_pending": 45,
     *       "priority_review_pending": 22,
     *       "average_wait_time": "2.5小时",
     *       "estimated_processing_time": {
     *         "auto": "10-30分钟",
     *         "manual": "2-4小时",
     *         "priority": "30分钟-1小时"
     *       }
     *     },
     *     "reviewer_status": {
     *       "online_reviewers": 8,
     *       "total_reviewers": 12,
     *       "current_load": "中等"
     *     },
     *     "recent_stats": {
     *       "reviews_today": 234,
     *       "approval_rate_today": 85.5,
     *       "average_score_today": 88.2
     *     }
     *   }
     * })
     */
    public function queueStatus(Request $request)
    {
        $result = $this->reviewService->getQueueStatus();

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(审核指南)
     * @ApiSummary(获取作品审核标准和指南)
     * @ApiMethod(GET)
     * @ApiRoute(/api/reviews/guidelines)
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "review_criteria": {
     *       "content_quality": {
     *         "name": "内容质量",
     *         "weight": 30,
     *         "description": "作品的创意性、完整性和表现力",
     *         "scoring_guide": {
     *           "excellent": "9-10分：创意独特，内容丰富，表现力强",
     *           "good": "7-8分：有一定创意，内容较完整",
     *           "average": "5-6分：内容基本完整，创意一般",
     *           "poor": "1-4分：内容简单，缺乏创意"
     *         }
     *       },
     *       "originality": {
     *         "name": "原创性",
     *         "weight": 25,
     *         "description": "作品的原创程度和独特性"
     *       },
     *       "compliance": {
     *         "name": "合规性",
     *         "weight": 25,
     *         "description": "是否符合平台规范和法律法规"
     *       },
     *       "technical_quality": {
     *         "name": "技术质量",
     *         "weight": 20,
     *         "description": "技术实现的质量和稳定性"
     *       }
     *     },
     *     "prohibited_content": [
     *       "违法违规内容",
     *       "色情暴力内容",
     *       "侵犯他人权益",
     *       "虚假误导信息",
     *       "恶意营销内容"
     *     ],
     *     "best_practices": [
     *       "确保内容原创性",
     *       "提供清晰的作品描述",
     *       "使用准确的标签分类",
     *       "保证技术质量",
     *       "遵守社区规范"
     *     ],
     *     "appeal_process": {
     *       "time_limit": "审核结果公布后7天内",
     *       "required_info": ["申诉理由", "补充证据"],
     *       "processing_time": "3-5个工作日",
     *       "appeal_limit": "每个作品最多申诉2次"
     *     }
     *   }
     * })
     */
    public function guidelines(Request $request)
    {
        $result = $this->reviewService->getReviewGuidelines();

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(快速预检)
     * @ApiSummary(发布前的快速内容预检)
     * @ApiMethod(POST)
     * @ApiRoute(/api/reviews/pre-check)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="resource_id", type="int", required=true, description="资源ID")
     * @ApiParams(name="title", type="string", required=true, description="作品标题")
     * @ApiParams(name="description", type="string", required=false, description="作品描述")
     * @ApiParams(name="tags", type="array", required=false, description="标签数组")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "预检完成",
     *   "data": {
     *     "pre_check_id": 123,
     *     "overall_score": 85,
     *     "risk_level": "low",
     *     "estimated_approval_rate": 90,
     *     "suggestions": [
     *       "建议优化作品描述，增加更多细节",
     *       "标签选择准确，有助于作品发现"
     *     ],
     *     "potential_issues": [],
     *     "recommended_review_type": "auto",
     *     "estimated_review_time": "10-30分钟"
     *   }
     * })
     */
    public function preCheck(Request $request)
    {
        $rules = [
            'resource_id' => 'required|integer|exists:resources,id',
            'title' => 'required|string|min:2|max:100',
            'description' => 'sometimes|string|max:1000',
            'tags' => 'sometimes|array|max:10',
            'tags.*' => 'string|max:20'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $checkData = [
            'resource_id' => $request->resource_id,
            'title' => $request->title,
            'description' => $request->get('description'),
            'tags' => $request->get('tags', [])
        ];

        $result = $this->reviewService->performPreCheck($user->id, $checkData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
