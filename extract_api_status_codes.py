#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从apitest-code.mdc提取每个接口的状态码映射
基于文档顺序生成精确的状态码映射表
"""

import re
import sys

def extract_api_status_codes():
    """从apitest-code.mdc提取每个接口的状态码"""
    file_path = "apitest-code.mdc"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 开始提取接口状态码...")
        print("=" * 60)
        
        # 匹配接口模式：编号、名称、方法、路径、错误响应
        pattern = r'- \[ \] \*\*(\d+)\*\* (.+?) `(\w+) ([^`]+)`\n(?:(?:  - [^\n]*\n)*?)  - 错误响应：([^\n]+)'
        
        matches = re.findall(pattern, content, re.MULTILINE)
        
        api_status_codes = {}
        
        for match in matches:
            api_num = match[0]
            api_name = match[1]
            method = match[2]
            path = match[3]
            error_response = match[4]
            
            # 提取状态码
            status_codes = []
            # 匹配 `数字` - 描述 的模式
            code_pattern = r'`(\d+)` - ([^,`]+)'
            code_matches = re.findall(code_pattern, error_response)
            
            for code, desc in code_matches:
                status_codes.append(int(code))
            
            api_status_codes[api_num] = {
                'name': api_name,
                'method': method,
                'path': path,
                'status_codes': sorted(status_codes),
                'error_response': error_response
            }
            
            print(f"接口 {api_num}: {api_name} {method} {path}")
            print(f"  状态码: {sorted(status_codes)}")
            print()
        
        print(f"✅ 提取完成！共提取了 {len(matches)} 个接口")
        
        # 生成状态码映射文件
        generate_status_codes_mapping(api_status_codes)
        
        return api_status_codes
        
    except Exception as e:
        print(f"❌ 提取失败: {e}")
        sys.exit(1)

def generate_status_codes_mapping(api_status_codes):
    """生成状态码映射文件"""
    
    # 状态码描述映射
    status_descriptions = {
        200: "成功",
        401: "未登录",
        403: "无权限", 
        404: "内容不存在",
        422: "验证错误",
        500: "发生异常",
        1001: "TOKEN无效",
        1004: "用户未注册",
        1005: "用户已存在",
        1006: "积分不足",
        1007: "无效操作",
        1008: "重复操作",
        1009: "邮箱已存在",
        1010: "参数无效",
        1011: "系统错误",
        1012: "服务不可用",
        1013: "模型不可用",
        1014: "配额超限",
        1015: "内容被过滤",
        1016: "处理超时",
        1020: "文件不存在",
        1021: "文件过大",
        1022: "文件类型不支持",
        1023: "上传失败",
        1030: "项目不存在",
        1031: "项目访问被拒绝",
        1032: "项目数量超限"
    }
    
    mapping_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于apitest-code.mdc的接口状态码映射
自动生成的精确状态码映射表
"""

import re
import sys

# 状态码描述映射
STATUS_DESCRIPTIONS = {status_descriptions}

# 接口状态码映射（按apitest-code.mdc顺序）
API_STATUS_CODES = {{
'''
    
    for api_num, data in sorted(api_status_codes.items(), key=lambda x: int(x[0])):
        mapping_content += f'    "{api_num}": {data["status_codes"]},  # {data["name"]} {data["method"]} {data["path"]}\n'
    
    mapping_content += '''
}

def update_api_status_codes():
    """基于映射表更新apitest-code.mdc中的接口状态码"""
    file_path = "apitest-code.mdc"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🚀 开始更新接口状态码...")
        print("=" * 60)
        
        updated_count = 0
        
        # 匹配接口模式
        pattern = r'(- \\[ \\] \\*\\*(\\d+)\\*\\* (.+?) `(\\w+) ([^`]+)`\\n)((?:  - [^\\n]+\\n)*)(  - 错误响应：[^\\n]+)'
        
        def update_error_response(match):
            nonlocal updated_count
            
            api_header = match.group(1)
            api_num = match.group(2)
            api_name = match.group(3)
            method = match.group(4)
            path = match.group(5)
            params_and_success = match.group(6)
            old_error_response = match.group(7)
            
            # 获取映射的状态码
            if api_num in API_STATUS_CODES:
                status_codes = API_STATUS_CODES[api_num]
                
                # 构建新的错误响应
                error_parts = []
                for code in status_codes:
                    if code in STATUS_DESCRIPTIONS:
                        error_parts.append(f"`{code}` - {STATUS_DESCRIPTIONS[code]}")
                
                if error_parts:
                    new_error_response = f"  - 错误响应：{', '.join(error_parts)}"
                    updated_count += 1
                    print(f"更新接口 {api_num}: {api_name}")
                    return api_header + params_and_success + new_error_response
            
            # 如果没有找到映射，保持原样
            return match.group(0)
        
        # 执行替换
        new_content = re.sub(pattern, update_error_response, content, flags=re.MULTILINE)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 状态码更新完成！")
        print(f"📊 更新统计: 共更新了 {updated_count} 个接口")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("选择操作:")
    print("1. 提取接口状态码")
    print("2. 更新接口状态码")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        extract_api_status_codes()
    elif choice == "2":
        update_api_status_codes()
    else:
        print("无效选择")
'''
    
    with open("api_status_codes_mapping.py", "w", encoding="utf-8") as f:
        f.write(mapping_content)
    
    print("✅ 状态码映射文件已生成: api_status_codes_mapping.py")

if __name__ == "__main__":
    extract_api_status_codes()
