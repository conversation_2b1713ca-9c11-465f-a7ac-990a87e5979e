<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="">
        <title>接口测试</title>
        <link href="/static/css/bootstrap.min.css" rel="stylesheet">
         <link rel="stylesheet" href="/static/css/all.min.css">
        <script src="/static/js/jquery.min.js"></script>
        <script src="/static/js/bootstrap.min.js"></script>
        <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
        <!--[if lt IE 9]>
        <script src="https://cdn.staticfile.org/html5shiv/3.7.3/html5shiv.min.js"></script>
        <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
        <![endif]-->
        <style type="text/css">
            body {
                padding-top: 70px; margin-bottom: 15px;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                font-family: "Roboto", "SF Pro SC", "SF Pro Display", "SF Pro Icons", "PingFang SC", BlinkMacSystemFont, -apple-system, "Segoe UI", "Microsoft Yahei", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
                font-weight: 400;
            }
            h2        { font-size: 1.6em; }
            hr        { margin-top: 10px; }
            .tab-pane { padding-top: 10px; }
            .mt0      { margin-top: 0px; }
            .footer   { font-size: 12px; color: #666; }
            .label    { display: inline-block; min-width: 65px; padding: 0.3em 0.6em 0.3em; }
            .string   { color: green; }
            .number   { color: darkorange; }
            .boolean  { color: blue; }
            .null     { color: magenta; }
            .key      { color: red; }
            .popover  { max-width: 400px; max-height: 400px; overflow-y: auto;}
            .list-group.panel > .list-group-item {
            }
            .list-group-item:last-child {
                border-radius:0;
            }
            h4.panel-title a {
                font-weight:normal;
                font-size:14px;
            }
            h4.panel-title a .text-muted {
                font-size:12px;
                font-weight:normal;
                font-family: 'Verdana';
            }
            #sidebar {
                width: 220px;
                position: fixed;
                margin-left: -240px;
                overflow-y:auto;
            }
            #sidebar > .list-group {
                margin-bottom:0;
            }
            #sidebar > .list-group > a{
                text-indent:0;
            }
            #sidebar .child {
                border:1px solid #ddd;
                border-bottom:none;
            }
            #sidebar .child > a {
                border:0;
            }
            #sidebar .list-group a.current {
                background:#f5f5f5;
            }
            @media (max-width: 1620px){
                #sidebar {
                    margin:0;
                }
                #accordion {
                    padding-left:235px;
                }
            }
            @media (max-width: 768px){
                #sidebar {
                    display: none;
                }
                #accordion {
                    padding-left:0px;
                }
            }
            .label-primary { background-color: #248aff;}
            .list-group>a{font-weight: bold;}
        </style>
    </head>
    <body>
        <div style="width: 100%;position: fixed;top: 100px;text-align: center;">
            <div class="alert alert-success hide" style="width: 200px;margin: auto;z-index: 999;">复制成功！</div>
        </div>
        <div class="navbar navbar-default navbar-fixed-top" role="navigation">
            <div class="container">
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                        <span class="sr-only">Toggle navigation</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand" href="./" target="_blank">接口测试</a>
                </div>
                <div class="navbar-collapse collapse">
                    <form class="navbar-form navbar-right">
                        <div class="form-group">
                            Authorization:
                        </div>
                        <div class="form-group">
                            <input type="text" class="form-control input-sm" data-toggle="tooltip" title="Authorization在会员注册或登录后都会返回,WEB端同时存在于Cookie中" placeholder="Authorization" id="Authorization" value="Bearer "/>
                        </div>
                        <div class="form-group">
                            Apiurl:
                        </div>
                        <div class="form-group">
                            <input id="apiUrl" type="text" class="form-control input-sm" data-toggle="tooltip" title="API接口URL" placeholder="https://api.mydomain.com" value="" />
                        </div>
                        <div class="form-group">
                            <button type="button" class="btn btn-success btn-sm" data-toggle="tooltip" title="点击保存后Authorization和Api url都将保存在本地Localstorage中" id="save_data">
                                <span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="container">
            <div id="sidebar"><div class="list-group panel"><a href="#广告接口（wss）" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">广告接口（wss）  <i class="fa fa-caret-down"></i></a><div class="child collapse" id="广告接口（wss）"><a href="javascript:;" data-id="0" class="list-group-item">广告开始</a><a href="javascript:;" data-id="1" class="list-group-item">广告结束</a></div><a href="#用户接口" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">用户接口  <i class="fa fa-caret-down"></i></a><div class="child collapse" id="用户接口"><a href="javascript:;" data-id="2" class="list-group-item">注册</a><a href="javascript:;" data-id="3" class="list-group-item">登录</a><a href="javascript:;" data-id="4" class="list-group-item">检测token是否有效</a></div></div></div><div class="panel-group" id="accordion"><h2>广告接口（wss）</h2><hr><div class="panel panel-default">
                    <div class="panel-heading" id="heading-0">
                        <div class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion0" href="#collapseOne0">
                                <span class="label label-primary">POST</span>
                                <span>广告开始</span>
                                <span>ad.store</span>
                            </a>
                            <button style="float: right" class="btn btn-primary btn-xs copy" data-route="ad.store">复制链接</button>
                        </div>
                    </div>
                    <div id="collapseOne0" class="panel-collapse collapse">
                        <div class="panel-body">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" id="doctab0">
                                <li class="active"><a href="#info0" data-toggle="tab">基础信息</a></li>
                                <li><a href="#sandbox0" data-toggle="tab">在线测试</a></li>
                                <li><a href="#sample0" data-toggle="tab">JSON</a></li>
                            </ul>
                            <!-- Tab panes -->
                            <div class="tab-content">
                                <div class="tab-pane active" id="info0">
                                    <div class="well"></div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求头Header</strong></div>
                                        <div class="panel-body">无
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求参数Query</strong></div>
                                        <div class="panel-body"><table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>ad_id</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>广告ID</td>
                                    </tr></tbody>
                            </table></div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>响应结果</strong></div>
                                        <div class="panel-body">
                                            <table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>├─code</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>状态码，200：成功，400：失败，401：未登录，403：无权限，404：内容不存在，405：网络请求方法错误，422：验证错误，500：发生异常，1001：TOEKN无效，1002：用户未注册，1003：用户已存在，1091：事件数已达限额</td>
                                    </tr><tr>
                                        <td>├─message</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>消息</td>
                                    </tr><tr>
                                        <td>├─data</td>
                                        <td>object</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>数据</td>
                                    </tr><tr>
                                        <td>│&nbsp;└─id</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>广告记录ID</td>
                                    </tr></tbody>
                            </table>
                                        </div>
                                    </div>
                                </div><!-- #info -->
                                <div class="tab-pane" id="sandbox0">
                                    <div class="row">
                                        <div class="col-md-12">
                                            
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <form enctype="application/x-www-form-urlencoded" role="form" action="ad.store" method="POST" name="form0" id="form0">
                                                        <div class="form-group">
                                                            <label class="control-label">ad_id</label>
                                                            <input type="number" class="form-control input-sm" required placeholder="广告ID" name="ad_id">
                                                        </div>
                                                        <div class="form-group">
                                                            <button type="submit" class="btn btn-success send" rel="0">提交</button>
                                                            <button type="reset" class="btn btn-info" rel="0">重置</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                            <pre id="response_headers0"></pre>
                                                            <pre id="response0" class="wss_res responsead.store"></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- #sandbox -->

                                <div class="tab-pane" id="sample0">
                                    <div class="row">
                                        <div class="col-md-12">

                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求头Header</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_header0" rows="3" class="form-control"></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_query0" rows="5" class="form-control">{"ad_id":""}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="padding: 10px 0;"><button class="btn btn-primary execute" data-index="0">执行</button></div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <pre id="sample_response_headers0"></pre>
                                                           <pre id="sample_response0">{
    "code": 200,
    "message": "success",
    "data": {
    "id": "584b4614-d127-4e49-a5ba-c76f86f41078"
    }
    }</pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div><!-- #sample -->

                            </div><!-- .tab-content -->
                        </div>
                    </div>
                </div><div class="panel panel-default">
                    <div class="panel-heading" id="heading-1">
                        <div class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion1" href="#collapseOne1">
                                <span class="label label-primary">POST</span>
                                <span>广告结束</span>
                                <span>ad.update</span>
                            </a>
                            <button style="float: right" class="btn btn-primary btn-xs copy" data-route="ad.update">复制链接</button>
                        </div>
                    </div>
                    <div id="collapseOne1" class="panel-collapse collapse">
                        <div class="panel-body">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" id="doctab1">
                                <li class="active"><a href="#info1" data-toggle="tab">基础信息</a></li>
                                <li><a href="#sandbox1" data-toggle="tab">在线测试</a></li>
                                <li><a href="#sample1" data-toggle="tab">JSON</a></li>
                            </ul>
                            <!-- Tab panes -->
                            <div class="tab-content">
                                <div class="tab-pane active" id="info1">
                                    <div class="well"></div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求头Header</strong></div>
                                        <div class="panel-body">无
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求参数Query</strong></div>
                                        <div class="panel-body"><table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>id</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>广告记录ID</td>
                                    </tr><tr>
                                        <td>time_length</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>时长（秒）</td>
                                    </tr><tr>
                                        <td>state</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>状态。0：进行中，1：加载失败，2：开始播放，3：离开，4：超时，5：结束</td>
                                    </tr></tbody>
                            </table></div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>响应结果</strong></div>
                                        <div class="panel-body">
                                            <table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>├─code</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>状态码，200：成功，400：失败，401：未登录，403：无权限，404：内容不存在，405：网络请求方法错误，422：验证错误，500：发生异常，1001：TOEKN无效，1002：用户未注册，1003：用户已存在，1091：事件数已达限额</td>
                                    </tr><tr>
                                        <td>├─message</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>消息</td>
                                    </tr><tr>
                                        <td>├─data</td>
                                        <td>array</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>数据</td>
                                    </tr></tbody>
                            </table>
                                        </div>
                                    </div>
                                </div><!-- #info -->
                                <div class="tab-pane" id="sandbox1">
                                    <div class="row">
                                        <div class="col-md-12">
                                            
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <form enctype="application/x-www-form-urlencoded" role="form" action="ad.update" method="POST" name="form1" id="form1">
                                                        <div class="form-group">
                                                            <label class="control-label">id</label>
                                                            <input type="text" class="form-control input-sm" required placeholder="广告记录ID" name="id">
                                                        </div><div class="form-group">
                                                            <label class="control-label">time_length</label>
                                                            <input type="number" class="form-control input-sm" required placeholder="时长（秒）" name="time_length">
                                                        </div><div class="form-group">
                                                            <label class="control-label">state</label>
                                                            <input type="number" class="form-control input-sm" required placeholder="状态。0：进行中，1：加载失败，2：开始播放，3：离开，4：超时，5：结束" name="state">
                                                        </div>
                                                        <div class="form-group">
                                                            <button type="submit" class="btn btn-success send" rel="1">提交</button>
                                                            <button type="reset" class="btn btn-info" rel="1">重置</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                            <pre id="response_headers1"></pre>
                                                            <pre id="response1" class="wss_res responsead.update"></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- #sandbox -->

                                <div class="tab-pane" id="sample1">
                                    <div class="row">
                                        <div class="col-md-12">

                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求头Header</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_header1" rows="3" class="form-control"></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_query1" rows="5" class="form-control">{"id":"","time_length":"","state":""}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="padding: 10px 0;"><button class="btn btn-primary execute" data-index="1">执行</button></div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <pre id="sample_response_headers1"></pre>
                                                           <pre id="sample_response1">{
    "code": 200,
    "message": "success",
    "data": []
    }</pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div><!-- #sample -->

                            </div><!-- .tab-content -->
                        </div>
                    </div>
                </div><h2>用户接口</h2><hr><div class="panel panel-default">
                    <div class="panel-heading" id="heading-2">
                        <div class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion2" href="#collapseOne2">
                                <span class="label label-primary">POST</span>
                                <span>注册</span>
                                <span>/api/auth/register</span>
                            </a>
                            <button style="float: right" class="btn btn-primary btn-xs copy" data-route="/api/auth/register">复制链接</button>
                        </div>
                    </div>
                    <div id="collapseOne2" class="panel-collapse collapse">
                        <div class="panel-body">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" id="doctab2">
                                <li class="active"><a href="#info2" data-toggle="tab">基础信息</a></li>
                                <li><a href="#sandbox2" data-toggle="tab">在线测试</a></li>
                                <li><a href="#sample2" data-toggle="tab">JSON</a></li>
                            </ul>
                            <!-- Tab panes -->
                            <div class="tab-content">
                                <div class="tab-pane active" id="info2">
                                    <div class="well"></div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求头Header</strong></div>
                                        <div class="panel-body">无
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求参数Query</strong></div>
                                        <div class="panel-body"><table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>username</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>用户帐号</td>
                                    </tr><tr>
                                        <td>password</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>用户密码</td>
                                    </tr></tbody>
                            </table></div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>响应结果</strong></div>
                                        <div class="panel-body">
                                            <table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>├─code</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>状态码，200：成功，400：失败，401：未登录，403：无权限，404：内容不存在，405：网络请求方法错误，422：验证错误，500：发生异常，1001：TOEKN无效，1002：用户未注册，1003：用户已存在，1091：事件数已达限额</td>
                                    </tr><tr>
                                        <td>├─message</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>消息</td>
                                    </tr><tr>
                                        <td>├─data</td>
                                        <td>object</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>数据</td>
                                    </tr><tr>
                                        <td>│&nbsp;├─token</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>工具平台Token</td>
                                    </tr><tr>
                                        <td>│&nbsp;├─user</td>
                                        <td>object</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>用户信息</td>
                                    </tr><tr>
                                        <td>│&nbsp;│&nbsp;└─id</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>用户ID</td>
                                    </tr></tbody>
                            </table>
                                        </div>
                                    </div>
                                </div><!-- #info -->
                                <div class="tab-pane" id="sandbox2">
                                    <div class="row">
                                        <div class="col-md-12">
                                            
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <form enctype="application/x-www-form-urlencoded" role="form" action="/api/auth/register" method="POST" name="form2" id="form2">
                                                        <div class="form-group">
                                                            <label class="control-label">username</label>
                                                            <input type="text" class="form-control input-sm" required placeholder="用户帐号" name="username">
                                                        </div><div class="form-group">
                                                            <label class="control-label">password</label>
                                                            <input type="text" class="form-control input-sm" required placeholder="用户密码" name="password">
                                                        </div>
                                                        <div class="form-group">
                                                            <button type="submit" class="btn btn-success send" rel="2">提交</button>
                                                            <button type="reset" class="btn btn-info" rel="2">重置</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                            <pre id="response_headers2"></pre>
                                                            <pre id="response2" class="wss_res response/api/auth/register"></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- #sandbox -->

                                <div class="tab-pane" id="sample2">
                                    <div class="row">
                                        <div class="col-md-12">

                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求头Header</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_header2" rows="3" class="form-control"></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_query2" rows="5" class="form-control">{"username":"","password":""}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="padding: 10px 0;"><button class="btn btn-primary execute" data-index="2">执行</button></div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <pre id="sample_response_headers2"></pre>
                                                           <pre id="sample_response2">{
        "code": 200,
        "message": "success",
        "data": {
            "token": "Ej2ZeyvnnauWwVI9F5f5NjWpQm83BTJ2ZOAkfgWD4KoJR",
            "user": {
            "id": 10
            }
        }
        }</pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div><!-- #sample -->

                            </div><!-- .tab-content -->
                        </div>
                    </div>
                </div><div class="panel panel-default">
                    <div class="panel-heading" id="heading-3">
                        <div class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion3" href="#collapseOne3">
                                <span class="label label-primary">POST</span>
                                <span>登录</span>
                                <span>/api/auth/login</span>
                            </a>
                            <button style="float: right" class="btn btn-primary btn-xs copy" data-route="/api/auth/login">复制链接</button>
                        </div>
                    </div>
                    <div id="collapseOne3" class="panel-collapse collapse">
                        <div class="panel-body">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" id="doctab3">
                                <li class="active"><a href="#info3" data-toggle="tab">基础信息</a></li>
                                <li><a href="#sandbox3" data-toggle="tab">在线测试</a></li>
                                <li><a href="#sample3" data-toggle="tab">JSON</a></li>
                            </ul>
                            <!-- Tab panes -->
                            <div class="tab-content">
                                <div class="tab-pane active" id="info3">
                                    <div class="well"></div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求头Header</strong></div>
                                        <div class="panel-body">无
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求参数Query</strong></div>
                                        <div class="panel-body"><table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>username</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>用户帐号</td>
                                    </tr><tr>
                                        <td>password</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>用户密码</td>
                                    </tr></tbody>
                            </table></div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>响应结果</strong></div>
                                        <div class="panel-body">
                                            <table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>├─code</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>状态码，200：成功，400：失败，401：未登录，403：无权限，404：内容不存在，405：网络请求方法错误，422：验证错误，500：发生异常，1001：TOEKN无效，1002：用户未注册，1003：用户已存在，1091：事件数已达限额</td>
                                    </tr><tr>
                                        <td>├─message</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>消息</td>
                                    </tr><tr>
                                        <td>├─data</td>
                                        <td>object</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>数据</td>
                                    </tr><tr>
                                        <td>│&nbsp;├─token</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>工具平台Token</td>
                                    </tr><tr>
                                        <td>│&nbsp;├─user</td>
                                        <td>object</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>用户信息</td>
                                    </tr><tr>
                                        <td>│&nbsp;│&nbsp;└─id</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>用户ID</td>
                                    </tr></tbody>
                            </table>
                                        </div>
                                    </div>
                                </div><!-- #info -->
                                <div class="tab-pane" id="sandbox3">
                                    <div class="row">
                                        <div class="col-md-12">
                                            
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <form enctype="application/x-www-form-urlencoded" role="form" action="/api/auth/login" method="POST" name="form3" id="form3">
                                                        <div class="form-group">
                                                            <label class="control-label">username</label>
                                                            <input type="text" class="form-control input-sm" required placeholder="用户帐号" name="username">
                                                        </div><div class="form-group">
                                                            <label class="control-label">password</label>
                                                            <input type="text" class="form-control input-sm" required placeholder="用户密码" name="password">
                                                        </div>
                                                        <div class="form-group">
                                                            <button type="submit" class="btn btn-success send" rel="3">提交</button>
                                                            <button type="reset" class="btn btn-info" rel="3">重置</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                            <pre id="response_headers3"></pre>
                                                            <pre id="response3" class="wss_res response/api/auth/login"></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- #sandbox -->

                                <div class="tab-pane" id="sample3">
                                    <div class="row">
                                        <div class="col-md-12">

                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求头Header</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_header3" rows="3" class="form-control"></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_query3" rows="5" class="form-control">{"username":"","password":""}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="padding: 10px 0;"><button class="btn btn-primary execute" data-index="3">执行</button></div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <pre id="sample_response_headers3"></pre>
                                                           <pre id="sample_response3">{
        "code": 200,
        "message": "success",
        "data": {
            "token": "Ej2ZeyvnnauWwVI9F5f5NjWpQm83BTJ2ZOAkfgWD4KoJR",
            "user": {
            "id": 10
            }
        }
        }</pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div><!-- #sample -->

                            </div><!-- .tab-content -->
                        </div>
                    </div>
                </div><div class="panel panel-default">
                    <div class="panel-heading" id="heading-4">
                        <div class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion4" href="#collapseOne4">
                                <span class="label label-danger">GET</span>
                                <span>检测token是否有效</span>
                                <span>/api/check</span>
                            </a>
                            <button style="float: right" class="btn btn-primary btn-xs copy" data-route="/api/check">复制链接</button>
                        </div>
                    </div>
                    <div id="collapseOne4" class="panel-collapse collapse">
                        <div class="panel-body">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" id="doctab4">
                                <li class="active"><a href="#info4" data-toggle="tab">基础信息</a></li>
                                <li><a href="#sandbox4" data-toggle="tab">在线测试</a></li>
                                <li><a href="#sample4" data-toggle="tab">JSON</a></li>
                            </ul>
                            <!-- Tab panes -->
                            <div class="tab-content">
                                <div class="tab-pane active" id="info4">
                                    <div class="well"></div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求头Header</strong></div>
                                        <div class="panel-body"><table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>Authorization</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>API Token,格式：Bearer拼接空格再拼接token</td>
                                    </tr></tbody>
                            </table>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>请求参数Query</strong></div>
                                        <div class="panel-body"><table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>token</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>API Token</td>
                                    </tr></tbody>
                            </table></div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>响应结果</strong></div>
                                        <div class="panel-body">
                                            <table class="table table-bordered table-hover"><thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>必选</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody><tr>
                                        <td>├─code</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>状态码，200：成功，400：失败，401：未登录，403：无权限，404：内容不存在，405：网络请求方法错误，422：验证错误，500：发生异常，1001：TOEKN无效，1002：用户未注册，1003：用户已存在，1091：事件数已达限额</td>
                                    </tr><tr>
                                        <td>├─message</td>
                                        <td>string</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>消息</td>
                                    </tr><tr>
                                        <td>├─data</td>
                                        <td>object</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>数据</td>
                                    </tr><tr>
                                        <td>│&nbsp;└─id</td>
                                        <td>int</td>
                                        <td><i class="glyphicon glyphicon-ok text-success"></i></td>
                                        <td>用户ID</td>
                                    </tr></tbody>
                            </table>
                                        </div>
                                    </div>
                                </div><!-- #info -->
                                <div class="tab-pane" id="sandbox4">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求头Header</strong></div>
                                                <div class="panel-body">
                                                    <div class="headers"><div class="form-group">
                                                            <label class="control-label">Authorization</label>
                                                            <input type="text" class="form-control input-sm" required placeholder="API Token,格式：Bearer拼接空格再拼接token" name="Authorization">
                                                        </div> </div>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <form enctype="application/x-www-form-urlencoded" role="form" action="/api/check" method="GET" name="form4" id="form4">
                                                        <div class="form-group">
                                                            <label class="control-label">token</label>
                                                            <input type="text" class="form-control input-sm" required placeholder="API Token" name="token">
                                                        </div>
                                                        <div class="form-group">
                                                            <button type="submit" class="btn btn-success send" rel="4">提交</button>
                                                            <button type="reset" class="btn btn-info" rel="4">重置</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                            <pre id="response_headers4"></pre>
                                                            <pre id="response4" class="wss_res response/api/check"></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- #sandbox -->

                                <div class="tab-pane" id="sample4">
                                    <div class="row">
                                        <div class="col-md-12">

                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求头Header</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_header4" rows="3" class="form-control">{"Authorization":""}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>请求参数Query</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <textarea id="json_query4" rows="5" class="form-control">{"token":""}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="padding: 10px 0;"><button class="btn btn-primary execute" data-index="4">执行</button></div>
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><strong>响应输出</strong></div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-12" style="overflow-x:auto">
                                                           <pre id="sample_response_headers4"></pre>
                                                           <pre id="sample_response4">{
    "code": 200,
    "message": "success",
    "data": {
    "id": 11
    }
    }</pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div><!-- #sample -->

                            </div><!-- .tab-content -->
                        </div>
                    </div>
                </div></div><hr><div class="mt0 footer">Generated on 2025-07-12 00:20:42</div></div><script type="text/javascript">
            var token = localStorage.getItem('token');
            var host = '';
            // 使用环境变量配置的WebSocket地址
            host = 'wss://api.tiptop.cn:8080';
            if(window.location.hostname === 'localhost'){
                host = 'wss://localhost:8080';
            }
            var socket = new WebSocket(host);
            socket.onopen = function() {
                var currentTime = new Date().toLocaleString();
                console.log(currentTime + ' WebSocket connection opened');
                var msg = '{"event":"server.login","data":{"token": "'+token+'"}}';
                console.log(currentTime +' 发送: ' + msg);
                socket.send("{" + Base64.encode(msg) + "}");
            };
            socket.onmessage = function(event) {
                var currentTime = new Date().toLocaleString();
                var decode_msg = decode(event.data);
                console.log(currentTime + ' 接收: ' + decode_msg);
                if(decode_msg.includes('ping')){
                    var msg = '{"event":"pong"}';
                    console.log(currentTime +' 发送: ' + msg);
                    socket.send("{" + Base64.encode(msg) + "}");
                }else {
                    decode_msg = decode_msg.substring(0, decode_msg.lastIndexOf('}') + 1);
                    $('.in .wss_res').html(syntaxHighlight(JSON.stringify(JSON.parse(decode_msg), null, 2)));
                }
            };
            socket.onclose = function() {
                var currentTime = new Date().toLocaleString();
                console.log(currentTime + ' WebSocket connection closed');
            };
            function decode(msg){
                if(msg.length <= 2 || !msg.startsWith('{') || !msg.endsWith('}')){
                    return '';
                }
                msg = msg.slice(1, msg.length - 2);
                msg = Base64.decode(msg);
                msg = msg.replace('@','}');
                msg = msg.replace(/\\/g, "%");
                return unescape(msg);
            }
            var Base64 = {
                _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
                encode: function(e) {
                    var t = "";
                    var n, r, i, s, o, u, a;
                    var f = 0;
                    e = Base64._utf8_encode(e);
                    while (f < e.length) {
                        n = e.charCodeAt(f++);
                        r = e.charCodeAt(f++);
                        i = e.charCodeAt(f++);
                        s = n >> 2;
                        o = (n & 3) << 4 | r >> 4;
                        u = (r & 15) << 2 | i >> 6;
                        a = i & 63;
                        if (isNaN(r)) {
                            u = a = 64
                        } else if (isNaN(i)) {
                            a = 64
                        }
                        t = t + this._keyStr.charAt(s) + this._keyStr.charAt(o) + this._keyStr.charAt(u) + this._keyStr.charAt(a)
                    }
                    return t
                },
                decode: function(e) {
                    var t = "";
                    var n, r, i;
                    var s, o, u, a;
                    var f = 0;
                    e = e.replace(/[^A-Za-z0-9+/=]/g, "");
                    while (f < e.length) {
                        s = this._keyStr.indexOf(e.charAt(f++));
                        o = this._keyStr.indexOf(e.charAt(f++));
                        u = this._keyStr.indexOf(e.charAt(f++));
                        a = this._keyStr.indexOf(e.charAt(f++));
                        n = s << 2 | o >> 4;
                        r = (o & 15) << 4 | u >> 2;
                        i = (u & 3) << 6 | a;
                        t = t + String.fromCharCode(n);
                        if (u != 64) {
                            t = t + String.fromCharCode(r)
                        }
                        if (a != 64) {
                            t = t + String.fromCharCode(i)
                        }
                    }
                    t = Base64._utf8_decode(t);
                    return t
                },
                _utf8_encode: function(e) {
                    e = e.replace(/rn/g, "n");
                    var t = "";
                    for (var n = 0; n < e.length; n++) {
                        var r = e.charCodeAt(n);
                        if (r < 128) {
                            t += String.fromCharCode(r)
                        } else if (r > 127 && r < 2048) {
                            t += String.fromCharCode(r >> 6 | 192);
                            t += String.fromCharCode(r & 63 | 128)
                        } else {
                            t += String.fromCharCode(r >> 12 | 224);
                            t += String.fromCharCode(r >> 6 & 63 | 128);
                            t += String.fromCharCode(r & 63 | 128)
                        }
                    }
                    return t
                },
                _utf8_decode: function(e) {
                    var t = "";
                    var n = 0;
                    var r = c1 = c2 = 0;
                    while (n < e.length) {
                        r = e.charCodeAt(n);
                        if (r < 128) {
                            t += String.fromCharCode(r);
                            n++
                        } else if (r > 191 && r < 224) {
                            c2 = e.charCodeAt(n + 1);
                            t += String.fromCharCode((r & 31) << 6 | c2 & 63);
                            n += 2
                        } else {
                            c2 = e.charCodeAt(n + 1);
                            c3 = e.charCodeAt(n + 2);
                            t += String.fromCharCode((r & 15) << 12 | (c2 & 63) << 6 | c3 & 63);
                            n += 3
                        }
                    }
                    return t
                }
            }
            function syntaxHighlight(json) {
                if (typeof json != 'string') {
                    json = JSON.stringify(json, undefined, 2);
                }
                json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
                return json.replace(/("(\u[a-zA-Z0-9]{4}|\[^u]|[^\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                    var cls = 'number';
                    if (/^"/.test(match)) {
                        if (/:$/.test(match)) {
                            cls = 'key';
                        } else {
                            cls = 'string';
                        }
                    } else if (/true|false/.test(match)) {
                        cls = 'boolean';
                    } else if (/null/.test(match)) {
                        cls = 'null';
                    }
                    return '<span class="' + cls + '">' + match + '</span>';
                });
            }
            function prepareStr(str) {
                try {
                    return syntaxHighlight(JSON.stringify(JSON.parse(str.replace(/'/g, '"')), null, 2));
                } catch (e) {
                    return str;
                }
            }
            var storage = (function () {
                var uid = new Date;
                var storage;
                var result;
                try {
                    (storage = window.localStorage).setItem(uid, uid);
                    result = storage.getItem(uid) == uid;
                    storage.removeItem(uid);
                    return result && storage;
                } catch (exception) {
                }
            }());
            $.fn.serializeObject = function ()
            {
                var o = {};
                var a = this.serializeArray();
                $.each(a, function () {
                    if (!this.value) {
                        return;
                    }
                    if (o[this.name] !== undefined) {
                        if (!o[this.name].push) {
                            o[this.name] = [o[this.name]];
                        }
                        o[this.name].push(this.value || '');
                    } else {
                        o[this.name] = this.value || '';
                    }
                });
                return o;
            };
            $(document).ready(function () {
                 if (storage) {
                    storage.getItem('Authorization') && $('#Authorization').val(storage.getItem('Authorization'));
                    storage.getItem('apiUrl') && $('#apiUrl').val(storage.getItem('apiUrl'));
                }
                $('[data-toggle="tooltip"]').tooltip({
                    placement: 'bottom'
                });
                $(window).on("resize", function(){
                    $("#sidebar").css("max-height", $(window).height()-80);
                });
                $(window).trigger("resize");
                $(document).on("click", "#sidebar .list-group > .list-group-item", function(){
                    $("#sidebar .list-group > .list-group-item").removeClass("current");
                    $(this).addClass("current");
                });
                $(document).on("click", "#sidebar .child a", function(){
                    var heading = $("#heading-"+$(this).data("id"));
                    if(!heading.next().hasClass("in")){
                        $("a", heading).trigger("click");
                    }
                    $("html,body").animate({scrollTop:heading.offset().top-70});
                });
                $('code[id^=response]').hide();
                $.each($('pre[id^=sample_response],pre[id^=sample_post_body]'), function () {
                    if ($(this).html() == 'NA') {
                        return;
                    }
                    var str = prepareStr($(this).html());
                    $(this).html(str);
                });
                $.each($('textarea[id^=json_query],textarea[id^=json_header]'),function (){
                    if ($(this).val() == 'NA' || $(this).val() == '') {
                        return;
                    }
                    var str = JSON.stringify(JSON.parse($(this).val()),null,"	");
                    $(this).val(str);
                });
                $("[data-toggle=popover]").popover({placement: 'right'});
                $('[data-toggle=popover]').on('shown.bs.popover', function () {
                    var sample = $(this).parent().find(".popover-content"),
                            str = $(this).data('content');
                    if (typeof str == "undefined" || str === "") {
                        return;
                    }
                    var str = prepareStr(str);
                    sample.html('<pre>' + str + '</pre>');
                });
                $('body').on('click', '#save_data', function (e) {
                    if (storage) {
                        storage.setItem('Authorization', $('#Authorization').val());
                        storage.setItem('apiUrl', $('#apiUrl').val());
                    } else {
                        alert('您的浏览器不支持本地存储');
                    }
                });
                $('body').on('click', '.copy', function () {
                    var str = $(this).data('route');
                    var ele = document.createElement('input');
                    ele.setAttribute('value', str);
                    document.body.appendChild(ele);
                    ele.select();
                    document.execCommand('copy');
                    document.body.removeChild(ele);
                    $(".alert-success").addClass("show");
        window.setTimeout(function(){
            $(".alert-success").removeClass("show").addClass("hide");
        },1500);
                });
                $('body').on('click', '.execute',function (e){
                    e.preventDefault();
                    var index = $(this).data('index');
                    var url = $(this).parents('.panel').find('button.copy').data('route');
                    var method = $(this).parents('.panel').find('.panel-title a span').first().text();
                    var headers = {};
                    var data = {};
                    var headerParam = $("#json_header"+index).val();
                    var queryParam = $("#json_query"+index).val();
                    if(headerParam){
                       try{
                          headers = JSON.parse(headerParam);
                       }catch(e){
                           alert('JSON格式错误')
                           return false;
                       }
                    }
                    if(queryParam){
                       try{
                          data = JSON.parse(queryParam);
                       }catch(e){
                           alert('JSON格式错误')
                           return false;
                       }
                    }
                    $.ajax({
                        url: $('#apiUrl').val() + url,
                        data: data,
                        type: method,
                        dataType: 'json',
                        headers: headers,
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (data, textStatus, xhr) {
                            if (typeof data === 'object') {
                                var str = JSON.stringify(data, null, 2);
                                $('#sample_response' + index).html(syntaxHighlight(str));
                            } else {
                                $('#sample_response' + index).html(data || '');
                            }
                            $('#sample_response_headers' + index).html('HTTP ' + xhr.status + ' ' + xhr.statusText + '<br/><br/>' + xhr.getAllResponseHeaders());
                            //$('#sample_response_headers' + index).hide();
                        },
                        error: function (xhr, textStatus, error) {
                            try {
                                var str = JSON.stringify($.parseJSON(xhr.responseText), null, 2);
                            } catch (e) {
                                var str = xhr.responseText;
                            }
                            $('#sample_response' + index).html(syntaxHighlight(str));
                            $('#sample_response_headers' + index).html('HTTP ' + xhr.status + ' ' + xhr.statusText + '<br/><br/>' + xhr.getAllResponseHeaders());
                            //$('#sample_response_headers' + index).hide();
                        }
                    });
                });
                $('body').on('click', '.send', function (e) {
                    e.preventDefault();
                    var form = $(this).closest('form');
                    //added /g to get all the matched params instead of only first
                    var matchedParamsInRoute = $(form).attr('action').match(/[^{]+(?=\})/g);
                    var theId = $(this).attr('rel');
                    //keep a copy of action attribute in order to modify the copy
                    //instead of the initial attribute
                    var url = $(form).attr('action');
                    var method = $(form).prop('method').toLowerCase() || 'get';

                    var formData = new FormData();

                    $(form).find('input').each(function (i, input) {
                        if ($(input).attr('type').toLowerCase() == 'file') {
                            formData.append($(input).attr('name'), $(input)[0].files[0]);
                            method = 'post';
                        } else {
                            formData.append($(input).attr('name'), $(input).val())
                        }
                    });

                    var index, key, value;

                    if (matchedParamsInRoute) {
                        var params = {};
                        formData.forEach(function(value, key){
                            params[key] = value;
                        });
                        for (index = 0; index < matchedParamsInRoute.length; ++index) {
                            try {
                                key = matchedParamsInRoute[index];
                                value = params[key];
                                if (typeof value == "undefined")
                                    value = "";
                                url = url.replace("\{" + key + "\}", value);
                                formData.delete(key);
                            } catch (err) {
                                console.log(err);
                            }
                        }
                    }

                    var headers = {};

                    var token = $('#Authorization').val();
                    if (token.length > 0) {
                        headers['Authorization'] = token;
                    }

                    $("#sandbox" + theId + " .headers input").each(function () {
                        val = $(this).val();
                        if (val.length > 0) {
                            headers[$(this).prop('name')] = val;
                        }
                    });

                    if(url.includes('.'))
                    {
                         formData = new FormData($(form)[0]);
                        var data = {};
                        formData.forEach(function(value, key) {
                            if (key === 'avatar' && value instanceof File) {
                                data[key] = URL.createObjectURL(value);
                            } else {
                                data[key] = value;
                            }
                        });
                        var request = {
                            "event": url,
                            "data": data
                        }
                        socket.send("{" + Base64.encode(JSON.stringify(request)) + "}");
                    }else{
                        $.ajax({
                            url: $('#apiUrl').val() + url,
                            data: method == 'get' ? $(form).serialize() : formData,
                            type: method,
                            dataType: 'json',
                            contentType: false,
                            processData: false,
                            headers: headers,
                            xhrFields: {
                                withCredentials: true
                            },
                            success: function (data, textStatus, xhr) {
                                if (typeof data === 'object') {
                                    var str = JSON.stringify(data, null, 2);
                                    $('#response' + theId).html(syntaxHighlight(str));
                                } else {
                                    $('#response' + theId).html(data || '');
                                }
                                $('#response_headers' + theId).html('HTTP ' + xhr.status + ' ' + xhr.statusText + '<br/><br/>' + xhr.getAllResponseHeaders());
                                $('#response' + theId).show();
                            },
                            error: function (xhr, textStatus, error) {
                                try {
                                    var str = JSON.stringify($.parseJSON(xhr.responseText), null, 2);
                                } catch (e) {
                                    var str = xhr.responseText;
                                }
                                $('#response_headers' + theId).html('HTTP ' + xhr.status + ' ' + xhr.statusText + '<br/><br/>' + xhr.getAllResponseHeaders());
                                $('#response' + theId).html(syntaxHighlight(str));
                                $('#response' + theId).show();
                            }
                        });
                    }
                    return false;
                });
            });
</script></body></html>