<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\VoiceService;
use Illuminate\Http\Request;

/**
 * AI语音合成与音色管理
 */
class VoiceController extends Controller
{
    protected $voiceService;

    public function __construct(VoiceService $voiceService)
    {
        $this->voiceService = $voiceService;
    }

    /**
     * @ApiTitle (语音合成)
     * @ApiSummary (使用AI合成语音)
     * @ApiMethod (POST)
     * @ApiRoute (/voices/synthesize)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="text", type="string", required=true, description="要合成的文本内容")
     * @ApiParams (name="voice_id", type="string", required=false, description="音色ID")
     * @ApiParams (name="character_id", type="int", required=false, description="角色ID")
     * @ApiParams (name="speed", type="float", required=false, description="语速：0.5-2.0")
     * @ApiParams (name="pitch", type="float", required=false, description="音调：0.5-2.0")
     * @ApiParams (name="emotion", type="string", required=false, description="情感：neutral/happy/sad/angry/excited")
     * @ApiParams (name="language", type="string", required=false, description="语言：zh-CN/en-US/ja-JP")
     * @ApiParams (name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams (name="platform", type="string", required=false, description="指定AI平台：minimax/volcengine")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.estimated_cost", type="decimal", required=true, description="预估成本")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "语音合成任务创建成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "estimated_cost": "0.0500",
     *     "platform": "minimax"
     *   }
     * })
     */
    public function synthesize(Request $request)
    {
        $rules = [
            'text' => 'required|string|min:1|max:5000',
            'voice_id' => 'sometimes|string|max:50',
            'character_id' => 'sometimes|integer|exists:character_library,id',
            'speed' => 'sometimes|numeric|min:0.5|max:2.0',
            'pitch' => 'sometimes|numeric|min:0.5|max:2.0',
            'emotion' => 'sometimes|string|in:neutral,happy,sad,angry,excited,calm,energetic',
            'language' => 'sometimes|string|in:zh-CN,en-US,ja-JP,ko-KR',
            'project_id' => 'sometimes|integer|exists:projects,id',
            'platform' => 'sometimes|string|in:minimax,volcengine'
        ];

        $messages = [
            'text.required' => '要合成的文本内容不能为空',
            'text.min' => '文本内容至少1个字符',
            'text.max' => '文本内容不能超过5000个字符',
            'character_id.exists' => '角色不存在',
            'speed.min' => '语速不能小于0.5',
            'speed.max' => '语速不能大于2.0',
            'pitch.min' => '音调不能小于0.5',
            'pitch.max' => '音调不能大于2.0',
            'emotion.in' => '情感必须是：neutral、happy、sad、angry、excited、calm、energetic之一',
            'language.in' => '语言必须是：zh-CN、en-US、ja-JP、ko-KR之一',
            'project_id.exists' => '项目不存在',
            'platform.in' => 'AI平台必须是：minimax、volcengine之一'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $synthesisParams = [
            'voice_id' => $request->get('voice_id'),
            'speed' => $request->get('speed', 1.0),
            'pitch' => $request->get('pitch', 1.0),
            'emotion' => $request->get('emotion', 'neutral'),
            'language' => $request->get('language', 'zh-CN'),
            'platform' => $request->get('platform', 'minimax')
        ];

        $result = $this->voiceService->synthesizeVoice(
            $user->id,
            $request->text,
            $request->character_id,
            $request->project_id,
            $synthesisParams
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (语音合成状态查询)
     * @ApiSummary (查询语音合成任务的状态和结果)
     * @ApiMethod (GET)
     * @ApiRoute (/voices/{id}/status)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "voice_synthesis",
     *     "status": "completed",
     *     "platform": "minimax",
     *     "audio_url": "https://aiapi.tiptop.cn/voices/generated/123.mp3",
     *     "duration": 15.5,
     *     "file_size": "1.2MB",
     *     "cost": "0.0500",
     *     "processing_time_ms": 3000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:03"
     *   }
     * })
     */
    public function getStatus(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->voiceService->getVoiceStatus($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (批量语音合成)
     * @ApiSummary (批量合成多段语音)
     * @ApiMethod (POST)
     * @ApiRoute (/batch/voices/synthesize)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="texts", type="array", required=true, description="文本数组")
     * @ApiParams (name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams (name="common_params", type="object", required=false, description="通用参数")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "批量语音合成任务创建成功",
     *   "data": {
     *     "batch_id": "batch_123",
     *     "task_ids": [123, 124, 125],
     *     "total_count": 3,
     *     "estimated_cost": "0.1500"
     *   }
     * })
     */
    public function batchSynthesize(Request $request)
    {
        $rules = [
            'texts' => 'required|array|min:1|max:20',
            'texts.*' => 'required|string|min:1|max:5000',
            'project_id' => 'sometimes|integer|exists:projects,id',
            'common_params' => 'sometimes|array'
        ];

        $messages = [
            'texts.required' => '文本数组不能为空',
            'texts.min' => '至少需要1个文本',
            'texts.max' => '最多支持20个文本',
            'texts.*.required' => '文本不能为空',
            'texts.*.min' => '文本至少1个字符',
            'texts.*.max' => '文本不能超过5000个字符',
            'project_id.exists' => '项目不存在'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $result = $this->voiceService->batchSynthesizeVoices(
            $user->id,
            $request->texts,
            $request->project_id,
            $request->get('common_params', [])
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (音色克隆)
     * @ApiSummary (克隆指定音色)
     * @ApiMethod (POST)
     * @ApiRoute (/voices/clone)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="source_audio_url", type="string", required=true, description="源音频URL")
     * @ApiParams (name="voice_name", type="string", required=true, description="音色名称")
     * @ApiParams (name="description", type="string", required=false, description="音色描述")
     * @ApiParams (name="character_id", type="int", required=false, description="关联角色ID")
     * @ApiParams (name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams (name="platform", type="string", required=false, description="指定AI平台：minimax/volcengine")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "音色克隆任务创建成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "estimated_cost": "0.5000",
     *     "platform": "minimax"
     *   }
     * })
     */
    public function clone(Request $request)
    {
        $rules = [
            'source_audio_url' => 'required|url',
            'voice_name' => 'required|string|min:2|max:50',
            'description' => 'sometimes|string|max:200',
            'character_id' => 'sometimes|integer|exists:character_library,id',
            'project_id' => 'sometimes|integer|exists:projects,id',
            'platform' => 'sometimes|string|in:minimax,volcengine'
        ];

        $messages = [
            'source_audio_url.required' => '源音频URL不能为空',
            'source_audio_url.url' => '源音频URL格式不正确',
            'voice_name.required' => '音色名称不能为空',
            'voice_name.min' => '音色名称至少2个字符',
            'voice_name.max' => '音色名称不能超过50个字符',
            'description.max' => '音色描述不能超过200个字符',
            'character_id.exists' => '角色不存在',
            'project_id.exists' => '项目不存在',
            'platform.in' => 'AI平台必须是：minimax、volcengine之一'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $cloneParams = [
            'voice_name' => $request->voice_name,
            'description' => $request->get('description'),
            'platform' => $request->get('platform', 'minimax')
        ];

        $result = $this->voiceService->cloneVoice(
            $user->id,
            $request->source_audio_url,
            $request->character_id,
            $request->project_id,
            $cloneParams
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (音色克隆状态查询)
     * @ApiSummary (查询音色克隆任务的状态)
     * @ApiMethod (GET)
     * @ApiRoute (/voices/clone/{id}/status)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="任务ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "voice_clone",
     *     "status": "completed",
     *     "platform": "minimax",
     *     "voice_id": "cloned_voice_123",
     *     "voice_name": "克隆音色",
     *     "cost": "0.5000",
     *     "processing_time_ms": 60000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:01:00"
     *   }
     * })
     */
    public function getCloneStatus(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->voiceService->getVoiceCloneStatus($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (自定义音色生成)
     * @ApiSummary (生成自定义音色)
     * @ApiMethod (POST)
     * @ApiRoute (/voices/custom)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="voice_config", type="object", required=true, description="音色配置")
     * @ApiParams (name="voice_name", type="string", required=true, description="音色名称")
     * @ApiParams (name="character_id", type="int", required=false, description="关联角色ID")
     * @ApiParams (name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams (name="platform", type="string", required=false, description="指定AI平台：minimax/volcengine")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "自定义音色生成任务创建成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "estimated_cost": "0.3000",
     *     "platform": "minimax"
     *   }
     * })
     */
    public function custom(Request $request)
    {
        $rules = [
            'voice_config' => 'required|array',
            'voice_config.gender' => 'required|string|in:male,female,neutral',
            'voice_config.age' => 'sometimes|string|in:child,young,adult,elderly',
            'voice_config.accent' => 'sometimes|string|max:50',
            'voice_config.tone' => 'sometimes|string|in:warm,cold,bright,deep,soft,strong',
            'voice_name' => 'required|string|min:2|max:50',
            'character_id' => 'sometimes|integer|exists:character_library,id',
            'project_id' => 'sometimes|integer|exists:projects,id',
            'platform' => 'sometimes|string|in:minimax,volcengine'
        ];

        $messages = [
            'voice_config.required' => '音色配置不能为空',
            'voice_config.gender.required' => '性别不能为空',
            'voice_config.gender.in' => '性别必须是：male、female、neutral之一',
            'voice_config.age.in' => '年龄必须是：child、young、adult、elderly之一',
            'voice_config.tone.in' => '音调必须是：warm、cold、bright、deep、soft、strong之一',
            'voice_name.required' => '音色名称不能为空',
            'voice_name.min' => '音色名称至少2个字符',
            'voice_name.max' => '音色名称不能超过50个字符',
            'character_id.exists' => '角色不存在',
            'project_id.exists' => '项目不存在',
            'platform.in' => 'AI平台必须是：minimax、volcengine之一'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $customParams = [
            'voice_config' => $request->voice_config,
            'voice_name' => $request->voice_name,
            'platform' => $request->get('platform', 'minimax')
        ];

        $result = $this->voiceService->customVoice(
            $user->id,
            $request->character_id,
            $request->project_id,
            $customParams
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (自定义音色状态查询)
     * @ApiSummary (查询自定义音色生成任务的状态)
     * @ApiMethod (GET)
     * @ApiRoute (/voices/custom/{id}/status)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="任务ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "voice_custom",
     *     "status": "completed",
     *     "platform": "minimax",
     *     "voice_id": "custom_voice_123",
     *     "voice_name": "自定义音色",
     *     "voice_config": {
     *       "gender": "female",
     *       "age": "young",
     *       "tone": "warm"
     *     },
     *     "cost": "0.3000",
     *     "processing_time_ms": 45000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:45"
     *   }
     * })
     */
    public function getCustomStatus(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->voiceService->getVoiceCustomStatus($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
