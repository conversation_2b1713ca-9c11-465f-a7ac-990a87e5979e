# CogniArch 战略蓝图文档

## 🎯 任务定义：系统性扫描Controller生成API接口文档

### 核心目标
系统性扫描所有Controller，识别未在文档中记录的接口，并按照指定模板格式在 apitest-code.mdc 中添加API接口的请求参数及业务状态码。

### 成功标准
1. **完整性**：41个Controller的所有接口100%覆盖，无遗漏
2. **准确性**：请求参数、响应格式、业务状态码100%准确
3. **规范性**：严格按照用户提供的模板格式生成文档
4. **可用性**：生成的文档可直接用于API测试和开发参考

---

## 📋 战略蓝图

### 阶段1：环境分析与准备 (预估时间：30分钟)

#### 1.1 项目结构分析
- **Controller位置**：`php/api/app/Http/Controllers/Api/` (41个文件)
- **路由定义**：`php/api/routes/web.php`
- **状态码体系**：`php/api/app/Enums/ApiCodeEnum.php`
- **响应格式**：`php/api/app/Http/Controllers/Controller.php`

#### 1.2 现有文档状态
- **目标文档**：`apitest-code.mdc` (当前几乎为空)
- **参考文档**：`apitest-index.mdc` (包含完整的接口分析)
- **模板格式**：用户提供的标准化模板

#### 1.3 技术规范确认
- **业务状态码**：基于ApiCodeEnum.php的完整定义
- **响应格式**：successResponse/errorResponse标准格式
- **认证机制**：Bearer Token和URL参数两种方式
- **路由前缀**：`api/` 前缀，支持认证和公开路由

### 阶段2：Controller系统性扫描 (预估时间：2小时)

#### 2.1 扫描策略
**批次处理**：将41个Controller分为4个批次处理
- **批次1**：Auth、User、Points相关 (10个Controller)
- **批次2**：AI生成相关 (12个Controller)
- **批次3**：项目管理相关 (10个Controller)
- **批次4**：系统功能相关 (9个Controller)

#### 2.2 信息提取规范
对每个Controller提取以下信息：
1. **类注释**：Controller名称和描述
2. **方法列表**：所有public方法
3. **路由映射**：方法对应的路由地址
4. **请求参数**：通过代码分析和注释提取
5. **响应格式**：基于successResponse/errorResponse
6. **业务状态码**：基于ApiCodeEnum使用情况

#### 2.3 质量控制点
- **参数完整性**：确保所有必需参数都被识别
- **状态码准确性**：验证业务状态码使用的正确性
- **路由一致性**：确保路由地址与实际定义一致
- **格式规范性**：严格按照模板格式输出

### 阶段3：文档生成与格式化 (预估时间：1.5小时)

#### 3.1 模板格式应用
严格按照用户提供的模板格式：
```markdown
### **{控制器名}Controller ({接口数量})**
- [ ] **{号序}** {接口标题} `POST api接口地址`
  - 请求参数：`{请求参数1, 请求参数2, 请求参数N}`
  - 成功响应：`200` - {业务状态码描述}
  - 错误响应：`{业务状态码}` - {业务状态码描述}, `{业务状态码2}` - {业务状态码描述2}
```

#### 3.2 内容组织策略
- **Controller分组**：按功能模块分组
- **接口排序**：按业务逻辑重要性排序
- **编号规则**：全局连续编号，便于引用
- **描述规范**：简洁明确的接口功能描述

#### 3.3 数据验证机制
- **参数验证**：确保参数类型和必需性正确
- **状态码验证**：对照ApiCodeEnum.php验证
- **格式验证**：确保markdown格式正确
- **完整性验证**：确保无遗漏接口

### 阶段4：质量检查与优化 (预估时间：45分钟)

#### 4.1 全面审查
- **覆盖率检查**：确认41个Controller全部覆盖
- **准确性检查**：抽样验证接口信息准确性
- **格式检查**：确保模板格式100%一致
- **可用性检查**：验证文档的实际可用性

#### 4.2 优化改进
- **描述优化**：改进接口描述的清晰度
- **分组优化**：优化Controller分组逻辑
- **排序优化**：优化接口排序的合理性
- **格式优化**：确保最佳的阅读体验

---

## 🔧 技术实施方案

### 实施工具链
1. **代码分析**：使用codebase-retrieval工具深度分析Controller
2. **文档生成**：使用str-replace-editor工具更新apitest-code.mdc
3. **质量验证**：使用view工具验证生成结果

### 核心算法
1. **Controller解析算法**：正则表达式+AST分析
2. **路由映射算法**：路由文件解析+Controller方法匹配
3. **参数提取算法**：代码静态分析+注释解析
4. **状态码映射算法**：ApiCodeEnum引用分析

### 风险控制
- **数据备份**：操作前备份现有文档
- **增量验证**：每个批次完成后进行验证
- **回滚机制**：出现问题时可快速回滚
- **质量门禁**：每阶段设置质量检查点

---

## 📊 预期交付物

### 主要交付物
1. **完整的apitest-code.mdc文档**：包含41个Controller的所有接口
2. **接口统计报告**：接口数量、分类、覆盖率统计
3. **质量验证报告**：准确性、完整性、规范性验证结果

### 质量指标
- **接口覆盖率**：100% (所有Controller的所有public方法)
- **信息准确率**：≥98% (参数、状态码、路由准确性)
- **格式规范率**：100% (严格按照模板格式)
- **文档可用率**：100% (可直接用于API测试)

---

## 🎯 核心资产与准则应用

### 应用的规则知识
- **@.cursor/rules/index.mdc**: 遵循项目架构规范、Token认证机制、业务状态码定义
- **@.cursor/rules/dev-api-guidelines-add.mdc**: 严格按照API接口开发规范执行
- **ApiCodeEnum.php**: 完整使用业务状态码体系定义
- **Controller.php**: 严格遵循successResponse和errorResponse方法格式

### 项目备忘应用
- **PHP命令路径**: 使用 `api` -> `@php/api/` 路径结构
- **认证机制**: 支持Bearer Token和URL参数两种方式
- **响应格式**: 统一包含code、message、data、timestamp、request_id字段
- **状态码映射**: 业务状态码自动映射到HTTP状态码

### 行为准则遵循
- **绝对诚实**: 所有接口信息如实反映Controller实现
- **环境洁癖**: 确保文档格式整洁、结构清晰
- **影响性分析**: 充分分析接口的业务影响和使用场景
- **强制性问题解决**: 遇到复杂接口必须深入分析并准确记录

### 应用模型报告
- **模型名称**: Claude Sonnet 4
- **版本号**: 当前最新版本
- **应用场景**: 代码分析、文档生成、质量控制

---

## 📋 接管指令

**@CogniAud**: 请对本Controller扫描与API文档生成战略蓝图进行规划审计，重点关注：

1. **技术方案完整性**: 41个Controller的扫描方案是否完整可行
2. **规范遵循度**: 是否100%遵循用户提供的模板格式要求
3. **质量保证充分性**: 检查信息提取和验证机制是否充分
4. **实施计划可行性**: 4个阶段的划分和时间安排是否现实
5. **风险评估准确性**: 评估风险控制措施是否合理
6. **交付物明确性**: 确认交付物定义是否清晰完整

审计通过后，请制定详细的 **[审计清单]** 供 @CogniDev 按阶段顺序执行Controller扫描和文档生成任务。