---
description: API应用模块开发规划文档 - 修复编辑内容
globs: ["php/api/**/*.php", "php/api/**/*.json", "php/api/**/.env*", "php/api/**/routes/**", "php/api/**/config/**"]
alwaysApply: true
---

# API应用模块开发调试修复规范文档 (LongChec2严格制定)

## 📋 **文档权威性声明**

本文档是基于 `index.mdc` 规范要求和 `dev-api-guidelines-add.mdc` 开发规划流程制定的**开发过程中遇到问题进行调试修复的权威规范**。

### 🎯 **核心使命**
确保在修复或优化任何功能时，**绝对不会影响到同样调用了被修改公用方法或接口的其它功能**，实现零风险修复。

### 📚 **规范依据**
- **基础规范**: `@.cursor/rules/index.mdc` - 项目架构边界和技术栈规范
- **开发流程**: `@.cursor/rules/dev-api-guidelines-add.mdc` - 开发阶段拆分和模块整合原则
- **AI服务规范**: `@.cursor/rules/dev-aiapi-guidelines.mdc` - AI服务对接权威标准
- **数据迁移规范**: `index.mdc` 第718行 - 数据表迁移程序约定

### 🚨 **适用场景**
- ✅ **公用方法修复**: 修改被多个功能调用的公共方法
- ✅ **接口参数调整**: 修改API接口的请求或响应参数
- ✅ **数据库结构变更**: 修改数据表字段或索引
- ✅ **中间件逻辑修复**: 修改认证、权限、限流等中间件
- ✅ **AI服务调用修复**: 修复AI API调用失败、超时、格式错误
- ✅ **WebSocket服务修复**: 修复实时通信功能问题
- ✅ **积分系统修复**: 修复积分计算、冻结、返还机制
- ✅ **性能优化调整**: 优化查询、缓存、并发处理

---

## 🔍 **第一阶段：修复前全面风险评估 (强制执行)**

### 1.1 **公用方法依赖关系分析**

#### **依赖关系识别清单**
```yaml
公用方法影响范围分析 (必须100%完成):
  代码层面分析:
    - 🔍 使用IDE全局搜索功能，查找所有调用该方法的位置
    - 🔍 分析继承关系，检查父类/子类是否受影响
    - 🔍 检查接口实现，确认所有实现类的兼容性
    - 🔍 分析trait使用，确认所有使用该trait的类

  业务功能分析:
    - 📋 列出所有调用该方法的业务功能模块
    - 📋 分析功能间的数据流转关系
    - 📋 识别关键业务路径和次要功能路径
    - 📋 评估修改对用户体验的潜在影响

  数据库影响分析:
    - 🗄️ 检查数据表结构变更的影响范围
    - 🗄️ 分析外键约束和索引的影响
    - 🗄️ 评估数据迁移的复杂度和风险
    - 🗄️ 确认缓存数据的一致性要求

  API接口影响分析:
    - 🔗 检查接口参数变更对客户端的影响
    - 🔗 分析响应格式变更的兼容性
    - 🔗 评估接口版本控制的必要性
    - 🔗 确认第三方集成的影响范围
```

#### **风险等级评估矩阵**
```yaml
风险等级分类 (基于影响范围和复杂度):
  🔴 极高风险 (禁止直接修改):
    - 用户认证相关的核心方法
    - 积分系统的核心计算逻辑
    - AI服务连接器的核心方法
    - WebSocket服务的核心推送逻辑
    - 数据库事务处理的核心方法
    
  🟡 高风险 (需要完整测试方案):
    - 被5个以上功能调用的公用方法
    - 涉及金钱计算的业务逻辑
    - 影响系统性能的核心算法
    - 跨模块数据交互的接口方法
    
  🟢 中等风险 (需要回归测试):
    - 被2-4个功能调用的公用方法
    - 单一模块内的业务逻辑
    - 不涉及金钱的数据处理
    - 辅助功能的工具方法
    
  ⚪ 低风险 (需要单元测试):
    - 仅被1个功能调用的方法
    - 纯工具类的静态方法
    - 不涉及业务逻辑的格式化方法
    - 日志记录和调试相关方法
```

### 1.2 **架构合规性检查**

#### **架构边界验证清单**
```yaml
架构边界合规性检查 (基于index.mdc规范):
  职责边界验证:
    - ✅ 服务端职责: AI生成、数据管理、API服务、安全防护、监控告警
    - ✅ 客户端职责: 资源本地化、视频编辑、本地合成、UI交互、作品导出
    - ❌ 禁止服务端处理: 不储存且不中转用户创作过程中AI生成的资源、视频编辑处理、客户端UI逻辑、本地文件操作
    
  WebSocket使用边界:
    - ✅ 仅Python工具使用WebSocket进行实时通信
    - ❌ WEB网页工具禁用WebSocket连接
    - ✅ 仅用于AI生成进度推送和任务状态通知
    
  性能指标合规:
    - ✅ 并发支持: 1000用户同时使用 (index.mdc权威规范)
    - ✅ API响应时间: 平均200ms
    - ✅ AI生成时间: 文本15-30秒，图像30-60秒
    - ✅ 系统可用性: 99.9%
    
  安全机制合规:
    - ✅ Token验证机制完整
    - ✅ 密钥安全传输和使用后立即清理
    - ✅ 积分冻结/返还机制的事务安全
    - ✅ SQL注入和XSS防护机制
```

### 1.3 **技术栈兼容性检查**

#### **技术组件兼容性验证**
```yaml
技术栈兼容性检查 (基于index.mdc技术栈):
  PHP框架兼容性:
    - ✅ Lumen 10.x 框架特性兼容
    - ✅ Laravel组件版本兼容
    - ✅ 中间件和服务提供者兼容
    
  数据库兼容性:
    - ✅ MySQL 8.0.12 特性兼容
    - ✅ 数据表前缀 "p_" 规范遵循
    - ✅ 迁移程序规范遵循 (index.mdc约定)
    
  缓存系统兼容性:
    - ✅ Redis 7.4.2 特性兼容
    - ✅ 缓存键命名规范遵循
    - ✅ 缓存过期策略兼容
    
  AI服务兼容性:
    - ✅ dev-aiapi-guidelines.mdc 规范遵循
    - ✅ 虚拟服务地址: https://aiapi.tiptop.cn
    - ✅ 超时设置: 30秒 (遵循AI服务规范)
    - ✅ 支持平台: DeepSeek、LiblibAI、KlingAI、MiniMaxi
```

---

## 🛠️ **第二阶段：修复过程安全保障机制 (严格执行)**

### 2.1 **修复前准备工作**

#### **环境隔离和备份策略**
```yaml
修复环境准备 (必须100%完成):
  代码备份:
    - 🔄 创建当前代码的完整备份分支
    - 🔄 记录当前数据库结构快照
    - 🔄 备份相关配置文件
    - 🔄 记录当前系统运行状态
    
  测试环境准备:
    - 🧪 确保测试环境与生产环境一致
    - 🧪 准备完整的测试数据集
    - 🧪 配置监控和日志记录
    - 🧪 准备回滚脚本和恢复方案
    
  依赖关系文档:
    - 📋 详细记录所有依赖该方法的功能列表
    - 📋 绘制功能调用关系图
    - 📋 标记关键业务路径和次要路径
    - 📋 制定分阶段测试计划
```

#### **修复方案设计原则**
```yaml
修复方案设计 (基于dev-api-guidelines-add.mdc原则):
  最小化影响原则:
    - 🎯 优先考虑向后兼容的修复方案
    - 🎯 避免破坏性变更，采用渐进式修复
    - 🎯 保持接口签名稳定，内部逻辑优化
    - 🎯 使用适配器模式处理兼容性问题
    
  模块化修复原则:
    - 🧩 将复杂修复拆分为独立的小模块
    - 🧩 每个模块独立测试和验证
    - 🧩 支持单独回滚某个修复模块
    - 🧩 遵循开发阶段拆分原则 (3-5天/阶段)
    
  安全优先原则:
    - 🔒 所有涉及积分的修复必须保证事务安全
    - 🔒 用户认证相关修复必须保证安全性
    - 🔒 AI服务调用修复必须保证密钥安全
    - 🔒 数据库修复必须保证数据完整性
```

### 2.2 **修复过程监控机制**

#### **实时监控指标**
```yaml
修复过程监控 (自动化监控):
  性能指标监控:
    - ⚡ API响应时间变化 (目标: ≤200ms)
    - ⚡ 数据库查询性能变化
    - ⚡ 内存使用情况变化
    - ⚡ CPU使用率变化
    
  功能完整性监控:
    - ✅ 所有依赖功能的可用性检查
    - ✅ 关键业务流程的完整性验证
    - ✅ 数据一致性检查
    - ✅ 接口响应格式验证
    
  安全性监控:
    - 🔐 认证机制的有效性检查
    - 🔐 权限控制的正确性验证
    - 🔐 数据加密和传输安全检查
    - 🔐 SQL注入和XSS防护验证
    
  AI服务监控:
    - 🤖 AI服务连接状态检查
    - 🤖 AI API调用成功率监控
    - 🤖 AI服务响应时间监控
    - 🤖 AI服务错误率统计
```

#### **异常处理和熔断机制**
```yaml
异常处理策略 (自动化处理):
  性能异常处理:
    - 🚨 响应时间超过阈值 → 自动记录详细日志
    - 🚨 错误率超过0.1% → 触发告警机制
    - 🚨 系统负载过高 → 启动熔断保护
    - 🚨 数据库连接异常 → 自动切换备用连接
    
  功能异常处理:
    - 🔧 关键功能失效 → 立即停止修复进程
    - 🔧 数据不一致 → 触发数据修复流程
    - 🔧 接口调用失败 → 启动降级服务
    - 🔧 依赖服务异常 → 激活备用方案
    
  自动回滚触发条件:
    - 🔄 错误率超过1% → 自动回滚
    - 🔄 关键功能完全失效 → 立即回滚
    - 🔄 数据完整性受损 → 紧急回滚
    - 🔄 安全机制失效 → 强制回滚
```

### 2.3 **数据库修复安全机制**

#### **数据库变更安全策略**
```yaml
数据库修复安全 (基于index.mdc迁移规范):
  迁移程序规范:
    - 📊 必须使用框架规范的迁移程序 (index.mdc约定)
    - 📊 所有数据表必须使用 "p_" 前缀
    - 📊 迁移程序必须支持回滚操作
    - 📊 字段变更必须保证向后兼容
    
  事务安全保证:
    - 🔒 所有数据库修改必须在事务中执行
    - 🔒 使用数据库锁机制防止并发冲突
    - 🔒 关键数据修改必须有备份和恢复方案
    - 🔒 外键约束变更必须谨慎处理
    
  数据完整性验证:
    - ✅ 修改前后数据总量一致性检查
    - ✅ 关键字段数据格式验证
    - ✅ 外键关系完整性检查
    - ✅ 索引有效性验证
    
  性能影响评估:
    - ⚡ 查询性能变化评估
    - ⚡ 索引效率影响分析
    - ⚡ 存储空间使用变化
    - ⚡ 并发访问性能测试
```

---

## 🧪 **第三阶段：修复后全链路测试验证 (100%覆盖)**

### 3.1 **分层测试策略**

#### **单元测试层 (基础保障)**
```yaml
单元测试要求 (必须100%通过):
  被修改方法测试:
    - 🧪 修改后的方法功能正确性测试
    - 🧪 边界条件和异常情况测试
    - 🧪 性能基准测试 (响应时间≤200ms)
    - 🧪 内存使用和资源释放测试

  依赖方法测试:
    - 🔗 所有调用该方法的功能单元测试
    - 🔗 参数传递和返回值验证测试
    - 🔗 异常处理和错误传播测试
    - 🔗 并发调用安全性测试

  数据层测试:
    - 🗄️ 数据库操作的正确性测试
    - 🗄️ 事务完整性和回滚测试
    - 🗄️ 数据一致性和完整性测试
    - 🗄️ 缓存同步和失效测试
```

#### **集成测试层 (功能保障)**
```yaml
集成测试要求 (必须100%通过):
  业务流程测试:
    - 🔄 完整业务流程端到端测试
    - 🔄 跨模块数据流转测试
    - 🔄 异常情况下的业务连续性测试
    - 🔄 用户体验完整性测试

  API接口测试:
    - 🔗 所有相关API接口功能测试
    - 🔗 接口参数验证和响应格式测试
    - 🔗 接口性能和并发测试
    - 🔗 接口安全性和权限测试

  AI服务集成测试:
    - 🤖 AI服务调用功能测试 (基于dev-aiapi-guidelines.mdc)
    - 🤖 AI服务超时和错误处理测试
    - 🤖 AI服务降级和熔断测试
    - 🤖 AI服务监控和日志测试

  WebSocket服务测试:
    - 📡 WebSocket连接和推送功能测试
    - 📡 Python工具专用验证测试
    - 📡 实时通信稳定性测试
    - 📡 连接异常和重连测试
```

### 3.2 **回归测试策略**

#### **全功能回归测试清单**
```yaml
回归测试范围 (基于风险等级):
  🔴 极高风险修复 - 全系统回归测试:
    - 🔄 所有用户认证相关功能测试
    - 🔄 所有积分系统相关功能测试
    - 🔄 所有AI服务相关功能测试
    - 🔄 所有WebSocket相关功能测试
    - 🔄 所有数据库事务相关功能测试

  🟡 高风险修复 - 模块级回归测试:
    - 🔄 相关业务模块的完整功能测试
    - 🔄 跨模块交互功能测试
    - 🔄 性能基准对比测试
    - 🔄 安全机制验证测试

  🟢 中等风险修复 - 功能级回归测试:
    - 🔄 直接相关功能的完整测试
    - 🔄 间接影响功能的抽样测试
    - 🔄 关键业务路径测试
    - 🔄 用户体验验证测试

  ⚪ 低风险修复 - 基础回归测试:
    - 🔄 修改功能的完整测试
    - 🔄 相关功能的基础测试
    - 🔄 系统稳定性验证测试
    - 🔄 性能影响评估测试
```

#### **性能基准对比测试**
```yaml
性能测试要求 (基于index.mdc性能指标):
  API性能测试:
    - ⚡ 响应时间: 平均≤200ms (不得超过基准值的110%)
    - ⚡ 并发处理: 支持1000用户同时使用
    - ⚡ 吞吐量: 不得低于修复前的95%
    - ⚡ 错误率: ≤0.1%

  AI服务性能测试:
    - 🤖 文本生成: 15-30秒 (不得超过基准值的120%)
    - 🤖 图像生成: 30-60秒 (不得超过基准值的120%)
    - 🤖 服务可用性: ≥95%
    - 🤖 超时处理: 30秒内响应或正确超时

  数据库性能测试:
    - 🗄️ 查询响应时间不得超过基准值的110%
    - 🗄️ 事务处理时间不得超过基准值的110%
    - 🗄️ 并发访问性能不得低于基准值的95%
    - 🗄️ 缓存命中率不得低于基准值的95%

  WebSocket性能测试:
    - 📡 连接建立时间≤1秒
    - 📡 消息推送延迟≤100ms
    - 📡 并发连接数支持≥1000
    - 📡 连接稳定性≥99.9%
```

### 3.3 **用户验收测试**

#### **真实场景模拟测试**
```yaml
用户场景测试 (基于完整创作流程):
  完整业务流程测试:
    - 👤 用户认证登录流程测试
    - 📝 写故事(AI辅助)功能测试
    - 🎭 选形象(角色库)功能测试
    - 🖼️ 生成图像(多AI模型)功能测试
    - 🎬 客户端视频编辑配置测试
    - 📤 作品发布流程测试

  异常情况处理测试:
    - ❌ 网络中断恢复测试
    - ❌ AI服务异常处理测试
    - ❌ 积分不足处理测试
    - ❌ 权限异常处理测试
    - ❌ 数据异常恢复测试

  用户体验验证:
    - 😊 界面响应速度验证
    - 😊 操作流畅性验证
    - 😊 错误提示友好性验证
    - 😊 功能完整性验证
    - 😊 数据准确性验证
```

---

## 🔄 **第四阶段：应急回滚和恢复机制 (安全保障)**

### 4.1 **回滚策略设计**

#### **分层回滚机制**
```yaml
回滚策略分类 (基于修复复杂度):
  代码层回滚:
    - 🔄 特定文件版本回滚 (≤2分钟)
    - 🔄 配置文件独立回滚 (≤1分钟)
    - 🔄 依赖包版本回滚 (≤10分钟)

  数据库层回滚:
    - 🗄️ 迁移程序回滚 (基于Laravel迁移机制)
    - 🗄️ 数据快照恢复 (≤30分钟)
    - 🗄️ 增量数据回滚 (≤15分钟)
    - 🗄️ 索引结构回滚 (≤10分钟)

  缓存层回滚:
    - 💾 Redis缓存清理和重建 (≤5分钟)
    - 💾 缓存键结构回滚 (≤2分钟)
    - 💾 缓存策略回滚 (≤1分钟)
    - 💾 会话数据恢复 (≤3分钟)

  服务层回滚:
    - 🔧 AI服务配置回滚 (≤5分钟)
    - 🔧 WebSocket服务重启 (≤3分钟)
    - 🔧 中间件配置回滚 (≤2分钟)
    - 🔧 路由配置回滚 (≤1分钟)
```

#### **回滚触发条件**
```yaml
自动回滚触发 (实时监控):
  性能指标异常:
    - 🚨 API响应时间超过500ms持续5分钟 → 自动回滚
    - 🚨 错误率超过1%持续3分钟 → 自动回滚
    - 🚨 系统可用性低于99%持续5分钟 → 自动回滚
    - 🚨 数据库连接失败率超过5% → 立即回滚

  功能完整性异常:
    - 🔧 关键业务功能完全失效 → 立即回滚
    - 🔧 用户认证功能异常 → 立即回滚
    - 🔧 积分系统异常 → 立即回滚
    - 🔧 AI服务连接异常超过10分钟 → 自动回滚

  安全性异常:
    - 🔐 安全机制失效 → 强制回滚
    - 🔐 数据泄露风险 → 紧急回滚
    - 🔐 权限控制异常 → 立即回滚
    - 🔐 SQL注入攻击检测 → 强制回滚

  手动回滚触发:
    - 👤 LongDev1 发现严重问题 → 立即通知LongChec2
    - 👤 LongChec2 监控异常 → 强制回滚决策
    - 👤 用户反馈严重问题 → LongChec2验证后决策
```

### 4.2 **数据恢复机制**

#### **数据完整性保障**
```yaml
数据恢复策略 (基于数据重要性):
  核心业务数据:
    - 👤 用户账户数据 → 实时备份 + 多重验证
    - 💰 积分交易数据 → 事务日志 + 审计追踪
    - 🎭 角色库数据 → 版本控制 + 增量备份
    - 📝 作品数据 → 分布式存储 + 冗余备份

  系统配置数据:
    - ⚙️ AI服务配置 → 配置版本管理
    - ⚙️ 权限配置 → 配置快照备份
    - ⚙️ 路由配置 → 版本控制
    - ⚙️ 中间件配置 → 自动化备份

  临时数据处理:
    - 🔄 WebSocket会话数据 → 内存恢复
    - 🔄 缓存数据 → 自动重建
    - 🔄 临时文件 → 清理和重建
    - 🔄 日志数据 → 归档和压缩

  数据一致性验证:
    - ✅ 主从数据库一致性检查
    - ✅ 缓存与数据库一致性验证
    - ✅ 业务数据逻辑一致性检查
    - ✅ 外键约束完整性验证
```

### 4.3 **应急响应流程**

#### **应急响应时间线**
```yaml
应急响应流程 (分钟级响应):
  0-5分钟 (立即响应):
    - 🚨 问题发现和确认
    - 🚨 影响范围初步评估
    - 🚨 LongChec2 立即介入
    - 🚨 用户通知准备

  5-15分钟 (快速处理):
    - 🔧 问题根因初步分析
    - 🔧 回滚方案选择和执行
    - 🔧 系统状态监控
    - 🔧 用户影响评估

  15-30分钟 (恢复验证):
    - ✅ 系统功能完整性验证
    - ✅ 性能指标恢复确认
    - ✅ 用户体验验证
    - ✅ 数据完整性检查

  30-60分钟 (后续处理):
    - 📋 详细问题分析报告
    - 📋 改进措施制定
    - 📋 用户沟通和解释
    - 📋 经验总结和文档更新
```

---

## 🤖 **双 Agent 协作机制**

### **LongDev1 与 LongChec2 协作流程**
```yaml
双 Agent 协作机制:
  LongDev1 职责:
    - 🔧 执行具体的修复工作
    - 🔧 分析依赖关系和风险等级
    - 🔧 制定修复方案和实施计划
    - 🔧 执行测试和验证工作

  LongChec2 职责:
    - 🕵️‍♂️ 审查修复方案的合规性
    - 🕵️‍♂️ 监控修复过程的安全性
    - 🕵️‍♂️ 验证修复结果的完整性
    - 🕵️‍♂️ 决定是否需要回滚或继续

  协作触发点:
    - 📋 修复前: LongDev1 提交方案 → LongChec2 审批
    - 📋 修复中: 关键节点 → LongChec2 实时审查
    - 📋 修复后: LongDev1 汇报 → LongChec2 最终验证
    - 📋 异常时: 立即通知 → LongChec2 介入决策
```

### **技术支持资源**
- **规范文档**: `@.cursor/rules/` 目录
- **代码仓库**: 当前项目仓库
- **AI协作**: LongDev1 ↔ LongChec2 实时协作

---

## 🔧 **修复者必备工具清单 (LongChec2精简版)**

### **依赖关系分析 (强制执行)**
```yaml
修复者必须执行的分析步骤:
  1. 全局搜索调用位置:
    - 使用IDE全局搜索功能查找所有调用
    - 命令: grep -r "methodName" php/api/app/ --include="*.php"
    - 记录所有调用位置和调用上下文

  2. 检查继承和实现关系:
    - 分析父类/子类影响
    - 检查接口实现的兼容性
    - 确认trait使用的影响范围

  3. 数据库依赖检查:
    - 命令: php artisan migrate:status
    - 检查外键约束影响
    - 分析索引变更影响

  4. 风险等级判定:
    - 调用次数 > 10个 → 🔴 极高风险 (禁止直接修改)
    - 调用次数 5-10个 → 🟡 高风险 (需要完整测试)
    - 调用次数 2-4个 → 🟢 中等风险 (需要回归测试)
    - 调用次数 ≤ 1个 → ⚪ 低风险 (需要单元测试)
```

### **LongDev1 修复行为约束 (强制遵循)**
```yaml
LongDev1 在修复过程中的强制行为:
  修复前必须完成:
    - ✅ 完成依赖关系分析并记录结果
    - ✅ 确定风险等级并选择对应流程
    - ✅ 制定详细的修复方案和测试计划
    - ✅ 准备回滚方案和应急预案
    - ✅ 向 LongChec2 汇报修复计划并获得审批

  修复过程中必须遵循:
    - ✅ 严格按照制定的修复方案执行
    - ✅ 每个关键步骤都要记录执行结果
    - ✅ 发现异常立即停止并向 LongChec2 报告
    - ✅ 在关键节点暂停等待 LongChec2 审查

  修复后必须验证:
    - ✅ 所有依赖功能的完整性测试
    - ✅ 性能指标不得低于修复前95%
    - ✅ 安全机制的有效性验证
    - ✅ 向 LongChec2 提交完整的修复报告
```

---

## ⚡ **紧急修复决策流程 (LongChec2严格制定)**

### **紧急修复分级标准**
```yaml
紧急修复分级 (基于业务影响):
  🚨 P0级 - 系统完全不可用:
    - 用户无法登录系统
    - 核心API全部失效
    - 数据库连接完全中断
    - 处理时限: 30分钟内必须恢复

  🔥 P1级 - 核心功能异常:
    - 积分系统计算错误
    - AI服务调用失败
    - 用户认证异常
    - 处理时限: 2小时内必须修复

  ⚡ P2级 - 次要功能异常:
    - 非核心接口响应异常
    - 性能轻微下降
    - 部分功能体验问题
    - 处理时限: 1个工作日内修复

  📋 P3级 - 优化改进:
    - 代码质量优化
    - 性能微调
    - 用户体验改进
    - 处理时限: 按标准流程执行
```

### **LongDev1 紧急修复行为规范**
```yaml
不同级别的 LongDev1 行为要求:
  P0级紧急修复行为:
    - 🚨 可以跳过完整的依赖分析 (但必须记录风险)
    - 🚨 可以直接修改核心方法 (但必须立即通知 LongChec2)
    - 🚨 必须在修复后立即触发 LongChec2 审查
    - 🚨 LongChec2 必须实时监控整个修复过程

  P1级紧急修复行为:
    - 🔥 必须完成基础依赖分析 (15分钟内)
    - 🔥 可以简化测试流程但不能跳过
    - 🔥 必须在修复后立即向 LongChec2 汇报
    - 🔥 需要 LongChec2 确认修复方案

  P2级及以下修复行为:
    - ⚡ 严格按照标准流程执行
    - ⚡ 不允许跳过任何检查环节
    - ⚡ 必须完成所有规定的测试验证
    - ⚡ 按照 LongChec2 制定的审查流程
```

### **LongDev1 责任和义务 (不可推卸)**
```yaml
LongDev1 的核心责任:
  修复质量责任:
    - 🎯 确保修复不会引入新的问题
    - 🎯 确保所有依赖功能正常运行
    - 🎯 确保性能不会显著下降
    - 🎯 确保安全机制不会被破坏

  与 LongChec2 协作责任:
    - 📞 及时向 LongChec2 汇报修复计划和进度
    - 📞 主动报告遇到的问题和风险
    - 📞 积极配合 LongChec2 的审查和验证
    - 📞 详细记录修复过程和决策依据

  后续跟踪责任:
    - 📊 监控修复后的系统运行状态
    - 📊 及时处理修复引起的后续问题
    - 📊 总结修复经验并更新文档
    - 📊 协助 LongChec2 制定预防措施

  违规后果:
    - ⚠️ 未按规范执行 → LongChec2 强制介入
    - ⚠️ 隐瞒风险或问题 → LongChec2 取消修复权限
    - ⚠️ 造成系统故障 → LongChec2 强制回滚
    - ⚠️ 重复违规 → 永久禁止独立修复
```

---

## 📋 **修复执行检查清单 (每次修复强制完成)**

### **LongDev1 修复前强制检查清单**
```yaml
□ 完成公用方法依赖关系分析
□ 确定风险等级并选择对应流程
□ 制定详细的修复方案
□ 准备回滚方案和应急预案
□ 向 LongChec2 提交修复计划
□ 确认修复时间窗口和影响范围
□ 准备测试环境和测试数据
□ 获得 LongChec2 的审批和授权
```

### **LongDev1 修复过程强制检查清单**
```yaml
□ 严格按照制定的修复方案执行
□ 记录每个关键步骤的执行结果
□ 监控系统性能指标变化
□ 验证修复功能的正确性
□ 检查依赖功能的完整性
□ 确认安全机制的有效性
□ 发现异常立即停止并通知 LongChec2
□ 在关键节点暂停等待 LongChec2 审查
```

### **LongDev1 修复后强制检查清单**
```yaml
□ 执行所有依赖功能的完整性测试
□ 验证性能指标不低于修复前95%
□ 确认安全机制正常运行
□ 完成用户体验验证
□ 更新相关技术文档
□ 记录修复过程和经验教训
□ 制定后续监控计划
□ 向 LongChec2 提交完整修复报告
```

---

**LongChec2 最终声明**:

本规范专注于**规范 AI程序员 LongDev1 的修复行为**，确保在修复过程中不会影响其他功能。LongDev1 必须严格遵循本规范执行，任何偏离行为都将触发 LongChec2 的强制介入。

**双 Agent 核心原则**:
- 🤖 **LongDev1 执行**: 负责具体的修复实施工作
- 🕵️‍♂️ **LongChec2 监督**: 负责审查、监控和决策
- 🎯 **零影响修复**: 确保修复不会影响其他功能
- 🔄 **实时协作**: 关键节点必须暂停等待审查
- ⚡ **快速响应**: 异常情况立即触发协作机制

---

## 🎯 **AI资源与版本管理系统修复指导**

### **📋 修复场景识别**

#### **🔧 数据表结构修复**
**适用情况**：
- ✅ AI资源表字段冗余或缺失
- ✅ 版本表索引性能问题
- ✅ 积分关联字段缺失
- ✅ 项目上下文字段缺失

**修复原则**：
- 🎯 保持向后兼容性
- 🎯 优化查询性能
- 🎯 确保数据一致性

#### **🔧 API接口功能修复**
**适用情况**：
- ✅ 批量操作接口缺失
- ✅ 搜索统计功能不完善
- ✅ 权限检查性能问题
- ✅ 版本管理功能缺陷

**修复原则**：
- 🎯 遵循RESTful规范
- 🎯 保持接口语义清晰
- 🎯 确保参数验证完整

#### **🔧 业务逻辑安全修复**
**适用情况**：
- ✅ 版本号生成并发冲突
- ✅ 资源生成失败回滚不完整
- ✅ 权限检查缓存缺失
- ✅ 积分扣除安全风险

**修复原则**：
- 🎯 使用数据库事务保证一致性
- 🎯 实现完整的错误回滚机制
- 🎯 添加必要的缓存优化

### **⚠️ 修复注意事项**

#### **🚨 禁止修改的内容**
- ❌ 不得修改核心架构设计
- ❌ 不得改变职责边界定义
- ❌ 不得影响现有功能稳定性
- ❌ 不得违反index.mdc规范

#### **✅ 允许修复的内容**
- ✅ 数据表字段优化和索引添加
- ✅ API接口功能补充和性能优化
- ✅ 业务逻辑安全加固和错误处理
- ✅ 缓存策略优化和并发安全

### **🔄 修复验收标准**

#### **📊 技术验收标准**
- **代码质量**: 符合Laravel最佳实践
- **安全机制**: 事务、锁、异常处理完善
- **性能优化**: 索引、缓存策略合理
- **可维护性**: 注释清晰，逻辑清楚

#### **🎯 业务验收标准**
- **功能完整性**: 覆盖所有业务场景
- **用户体验**: 操作便利，响应及时
- **系统稳定性**: 错误处理完善，回滚机制健全
- **扩展性**: 支持未来功能扩展

**🎖️ LongChec2修复指导完成标记**：AI资源与版本管理系统修复指导已添加，为LongDev1提供明确的修复规范和验收标准。
