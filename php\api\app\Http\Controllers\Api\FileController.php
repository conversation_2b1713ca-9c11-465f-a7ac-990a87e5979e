<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\FileService;
use Illuminate\Http\Request;

/**
 * 文件上传管理与存储服务
 */
class FileController extends Controller
{
    protected $fileService;

    public function __construct(FileService $fileService)
    {
        $this->fileService = $fileService;
    }

    /**
     * @ApiTitle(文件上传)
     * @ApiSummary(上传文件到服务器)
     * @ApiMethod(POST)
     * @ApiRoute(/api/files/upload)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="file", type="file", required=true, description="上传的文件")
     * @ApiParams(name="folder", type="string", required=false, description="文件夹路径")
     * @ApiParams(name="is_public", type="boolean", required=false, description="是否公开")
     * @ApiParams(name="is_temporary", type="boolean", required=false, description="是否临时文件")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="文件信息")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "文件上传成功",
     *   "data": {
     *     "id": 123,
     *     "filename": "file_20240101_123456.jpg",
     *     "original_name": "photo.jpg",
     *     "file_url": "https://example.com/storage/files/file_20240101_123456.jpg",
     *     "file_type": "image",
     *     "file_size": 1024000,
     *     "human_file_size": "1.00 MB"
     *   }
     * })
     */
    public function upload(Request $request)
    {
        $rules = [
            'file' => 'required|file|max:10240', // 最大10MB
            'folder' => 'sometimes|string|max:255',
            'is_public' => 'sometimes|boolean',
            'is_temporary' => 'sometimes|boolean'
        ];

        $messages = [
            'file.required' => '请选择要上传的文件',
            'file.max' => '文件大小不能超过10MB',
            'folder.max' => '文件夹路径不能超过255个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->fileService->uploadFile(
            $user->id,
            $request->file('file'),
            $request->get('folder'),
            $request->boolean('is_public', false),
            $request->boolean('is_temporary', false)
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(文件列表)
     * @ApiSummary(获取用户文件列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/files/list)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="folder", type="string", required=false, description="文件夹路径")
     * @ApiParams(name="file_type", type="string", required=false, description="文件类型")
     * @ApiParams(name="keyword", type="string", required=false, description="搜索关键词")
     * @ApiParams(name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="文件列表")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "files": [
     *       {
     *         "id": 123,
     *         "filename": "file_20240101_123456.jpg",
     *         "original_name": "photo.jpg",
     *         "file_url": "https://example.com/storage/files/file_20240101_123456.jpg",
     *         "file_type": "image",
     *         "file_size": 1024000,
     *         "human_file_size": "1.00 MB",
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 50,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function getFiles(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $filters = [
            'folder' => $request->get('folder'),
            'file_type' => $request->get('file_type'),
            'keyword' => $request->get('keyword')
        ];

        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 20), 100);

        $result = $this->fileService->getUserFiles($user->id, $filters, $page, $perPage);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(文件详情)
     * @ApiSummary(获取文件详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/files/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="文件ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="文件详情")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "filename": "file_20240101_123456.jpg",
     *     "original_name": "photo.jpg",
     *     "file_url": "https://example.com/storage/files/file_20240101_123456.jpg",
     *     "file_type": "image",
     *     "mime_type": "image/jpeg",
     *     "file_size": 1024000,
     *     "human_file_size": "1.00 MB",
     *     "download_count": 5,
     *     "metadata": {},
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function getFileDetail(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->fileService->getFileDetail($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(删除文件)
     * @ApiSummary(删除用户文件)
     * @ApiMethod(DELETE)
     * @ApiRoute(/api/files/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="文件ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "文件删除成功",
     *   "data": {
     *     "file_id": 123,
     *     "deleted_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function deleteFile(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->fileService->deleteFile($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(文件下载)
     * @ApiSummary(下载文件)
     * @ApiMethod(GET)
     * @ApiRoute(/api/files/{id}/download)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="文件ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="下载信息")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "download_url": "https://example.com/storage/files/file_20240101_123456.jpg",
     *     "filename": "photo.jpg",
     *     "expires_at": "2024-01-01 13:00:00"
     *   }
     * })
     */
    public function downloadFile(Request $request, $id)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $result = $this->fileService->getDownloadUrl($id, $user->id);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
