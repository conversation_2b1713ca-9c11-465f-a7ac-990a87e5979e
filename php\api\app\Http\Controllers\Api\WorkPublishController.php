<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\WorkPublishService;
use App\Services\WorkPublishPermissionService;
use App\Models\WorkPlaza;
use App\Models\WorkInteraction;
use App\Enums\ApiCodeEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 🎯 作品发布管理控制器
 * 严格按照dev-api-guidelines-add.mdc规范实现
 * 对应规范第2832行：作品发布管理控制器
 */
class WorkPublishController extends Controller
{
    protected $workPublishService;
    protected $permissionService;

    public function __construct(
        WorkPublishService $workPublishService,
        WorkPublishPermissionService $permissionService
    ) {
        $this->workPublishService = $workPublishService;
        $this->permissionService = $permissionService;
    }

    /**
     * 🔧 CogniDev修复：发布作品 - 严格按照dev-api-guidelines-add.mdc第2907行标准
     * @ApiTitle(发布作品)
     * @ApiMethod(POST)
     * @ApiRoute(/works/publish)
     * @ApiParams({"title": "作品标题", "description": "作品描述", "file_path": "文件路径", "status": "发布状态"})
     * @ApiReturn({"code": 200, "message": "作品发布成功", "data": {"work_id": 123}})
     */
    public function publishWork(Request $request)
    {
        // 🔧 CogniDev修复：使用AuthService进行认证 - 按规范要求
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(
                $authResult['response']['code'],
                $authResult['response']['message'],
                $authResult['response']['data']
            );
        }

        // 🔧 CogniDev修复：严格按照dev-api-guidelines-add.mdc第2907行参数定义
        $rules = [
            'title' => 'required|string|min:2|max:200',
            'description' => 'sometimes|string|max:1000',
            'file_path' => 'required|string|max:500', // 文件路径而非文件上传
            'resource_id' => 'required|integer', // 添加资源ID验证
            'status' => 'sometimes|string|in:draft,published,private'
        ];

        $messages = [
            'title.required' => '作品标题不能为空',
            'title.min' => '作品标题至少2个字符',
            'title.max' => '作品标题不能超过200个字符',
            'file_path.required' => '文件路径不能为空',
            'file_path.max' => '文件路径不能超过500个字符',
            'resource_id.required' => '资源ID不能为空',
            'resource_id.integer' => '资源ID必须是整数',
            'status.in' => '发布状态无效'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        $user = $authResult['user'];

        // 🔧 CogniDev修复：添加资源验证和重复操作检查
        $resourceId = $request->resource_id;

        // 422 - 资源验证：检查资源是否存在且属于当前用户
        $resource = \Illuminate\Support\Facades\DB::table('ai_resources')
            ->where('id', $resourceId)
            ->where('user_id', $user->id)
            ->where('status', 'completed')  // 使用实际的状态枚举
            ->first();

        if (!$resource) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '资源ID不存在或未完成');
        }

        // 1006 - 重复操作检查：检查该资源是否已发布
        $existingPublication = \Illuminate\Support\Facades\DB::table('work_plaza')
            ->where('source_resource_id', $resourceId)
            ->where('user_id', $user->id)
            ->whereIn('publish_status', ['published', 'draft'])
            ->first();

        if ($existingPublication) {
            return $this->errorResponse(ApiCodeEnum::DUPLICATE_OPERATION, '该资源已发布或正在审核中');
        }

        try {
            // 创建作品发布记录
            $workId = \Illuminate\Support\Facades\DB::table('work_plaza')->insertGetId([
                'user_id' => $user->id,
                'work_uuid' => 'work-' . time() . '-' . rand(1000, 9999),
                'work_type' => 'character',  // 默认类型
                'source_resource_id' => $resourceId,
                'work_title' => $request->title,
                'work_description' => $request->get('description', ''),
                'file_name' => basename($request->file_path),
                'file_extension' => pathinfo($request->file_path, PATHINFO_EXTENSION),
                'file_size' => 1024,  // 模拟文件大小
                'mime_type' => 'application/octet-stream',
                'file_path' => $request->file_path,
                'publish_status' => $request->get('status', 'published'),
                'published_at' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            return $this->successResponse(['work_id' => $workId], '作品发布成功');
        } catch (\Exception $e) {
            return $this->errorResponse(ApiCodeEnum::ERROR, '作品发布失败: ' . $e->getMessage());
        }
    }

    /**
     * 🔧 LongDev1标记：编辑作品 - 按规范第2865行
     * @ApiTitle(编辑作品)
     * @ApiMethod(PUT)
     * @ApiRoute(/works/{id})
     * @ApiParams({"id": "作品ID", "title": "作品标题", "description": "作品描述"})
     * @ApiReturn({"code": 200, "message": "作品更新成功"})
     */
    public function update($id, Request $request)
    {
        // 🔧 LongDev1标记：使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(
                $authResult['response']['code'],
                $authResult['response']['message'],
                $authResult['response']['data']
            );
        }

        $rules = [
            'title' => 'sometimes|string|min:2|max:200',
            'description' => 'sometimes|string|max:1000',
            'tags' => 'sometimes|array|max:10',
            'tags.*' => 'string|max:20'
        ];

        $this->validateData($request->all(), $rules);

        $user = $authResult['user'];

        try {
            $work = WorkPlaza::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$work) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '作品不存在或无权限');
            }

            // 检查是否可以编辑
            if ($work->publish_status === WorkPlaza::PUBLISH_STATUS_PUBLISHED && $work->status === WorkPlaza::STATUS_MANUAL_REVIEW) {
                return $this->errorResponse(ApiCodeEnum::INVALID_OPERATION, '作品已发布且正在人工审核中，不能修改');
            }

            $updateData = $request->only(['title', 'description', 'tags']);
            if (isset($updateData['title'])) {
                $updateData['work_title'] = $updateData['title'];
                unset($updateData['title']);
            }
            if (isset($updateData['description'])) {
                $updateData['work_description'] = $updateData['description'];
                unset($updateData['description']);
            }
            if (isset($updateData['tags'])) {
                $updateData['work_tags'] = $updateData['tags'];
                unset($updateData['tags']);
            }

            $work->update($updateData);

            Log::info('作品更新成功', [
                'work_id' => $work->id,
                'user_id' => $user->id,
                'updated_fields' => array_keys($updateData)
            ]);

            return $this->successResponse([
                'work_id' => $work->id,
                'title' => $work->work_title,
                'description' => $work->work_description,
                'tags' => $work->work_tags,
                'updated_at' => $work->updated_at->format('Y-m-d H:i:s')
            ], '作品更新成功');

        } catch (\Exception $e) {
            Log::error('作品更新失败', [
                'work_id' => $id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse(ApiCodeEnum::ERROR, '作品更新失败：' . $e->getMessage());
        }
    }

    /**
     * 🔧 LongDev1标记：删除作品 - 按规范第2882行
     * @ApiTitle(删除作品)
     * @ApiMethod(DELETE)
     * @ApiRoute(/works/{id})
     * @ApiParams({"id": "作品ID"})
     * @ApiReturn({"code": 200, "message": "作品删除成功"})
     */
    public function delete($id, Request $request)
    {
        // 🔧 LongDev1标记：使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(
                $authResult['response']['code'],
                $authResult['response']['message'],
                $authResult['response']['data']
            );
        }

        $user = $authResult['user'];

        try {
            $work = WorkPlaza::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$work) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '作品不存在或无权限');
            }

            // 软删除
            $work->update([
                'publish_status' => WorkPlaza::PUBLISH_STATUS_DELETED
            ]);
            $work->delete();

            Log::info('作品删除成功', [
                'work_id' => $work->id,
                'user_id' => $user->id
            ]);

            return $this->successResponse([
                'work_id' => $work->id,
                'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
            ], '作品删除成功');

        } catch (\Exception $e) {
            Log::error('作品删除失败', [
                'work_id' => $id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse(ApiCodeEnum::ERROR, '作品删除失败：' . $e->getMessage());
        }
    }

    /**
     * 🔧 LongDev1标记：获取我的作品 - 按规范第2897行
     * @ApiTitle(获取我的作品)
     * @ApiMethod(GET)
     * @ApiRoute(/works/my-works)
     * @ApiParams({"work_type": "作品类型", "publish_status": "发布状态", "page": "页码"})
     * @ApiReturn({"code": 200, "message": "success", "data": {"works": [], "pagination": {}}})
     */
    public function myWorks(Request $request)
    {
        // 🔧 LongDev1标记：使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(
                $authResult['response']['code'],
                $authResult['response']['message'],
                $authResult['response']['data']
            );
        }

        $user = $authResult['user'];
        $filters = $request->only(['work_type', 'publish_status', 'per_page']);

        $result = $this->workPublishService->getMyWorks($user->id, $filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * 🔧 LongDev1标记：作品展示库 - 按规范第2914行
     * @ApiTitle(作品展示库)
     * @ApiMethod(GET)
     * @ApiRoute(/works/gallery)
     */
    public function gallery(Request $request)
    {
        $filters = $request->only(['work_type', 'featured', 'sort_by', 'per_page']);

        $result = $this->workPublishService->getGallery($filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * 🔧 LongDev1标记：获取分享链接 - 按规范第2929行
     * @ApiTitle(获取分享链接)
     * @ApiMethod(GET)
     * @ApiRoute(/works/{id}/share)
     * @ApiParams({"id": "作品ID", "type": "分享类型", "password": "分享密码"})
     * @ApiReturn({"code": 200, "message": "success", "data": {"share_url": "分享链接", "share_token": "分享令牌"}})
     */
    public function getShareLink($id, Request $request)
    {
        // 🔧 LongDev1标记：使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(
                $authResult['response']['code'],
                $authResult['response']['message'],
                $authResult['response']['data']
            );
        }

        $user = $authResult['user'];

        try {
            $work = WorkPlaza::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$work) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '作品不存在或无权限');
            }

            // 生成分享令牌
            $shareToken = \Illuminate\Support\Str::random(32);
            $shareType = $request->input('type', 'public');
            $sharePassword = $request->input('password');

            $share = \App\Models\WorkShare::create([
                'work_id' => $work->id,
                'share_token' => $shareToken,
                'share_type' => $shareType,
                'share_password' => $sharePassword,
                'expires_at' => $request->input('expires_at') ? \Carbon\Carbon::parse($request->input('expires_at')) : null,
                'max_access_count' => $request->input('max_access_count')
            ]);

            $shareUrl = url("/share/work/{$shareToken}");

            return $this->successResponse([
                'share_url' => $shareUrl,
                'share_token' => $shareToken,
                'share_type' => $shareType,
                'expires_at' => $share->expires_at ? $share->expires_at->format('Y-m-d H:i:s') : null
            ], '分享链接生成成功');

        } catch (\Exception $e) {
            Log::error('生成分享链接失败', [
                'work_id' => $id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse(ApiCodeEnum::ERROR, '生成分享链接失败：' . $e->getMessage());
        }
    }

    /**
     * 🔧 LongDev1标记：点赞作品 - 按规范第2947行
     * @ApiTitle(点赞作品)
     * @ApiMethod(POST)
     * @ApiRoute(/works/{id}/like)
     * @ApiParams({"id": "作品ID"})
     * @ApiReturn({"code": 200, "message": "点赞成功", "data": {"like_count": 123}})
     */
    public function like($id, Request $request)
    {
        // 🔧 LongDev1标记：使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(
                $authResult['response']['code'],
                $authResult['response']['message'],
                $authResult['response']['data']
            );
        }

        $user = $authResult['user'];

        try {
            $work = WorkPlaza::find($id);

            if (!$work || !$work->isPublished()) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '作品不存在或未发布');
            }

            // 检查是否已点赞
            $existingLike = WorkInteraction::where('work_id', $work->id)
                ->where('user_id', $user->id)
                ->where('interaction_type', WorkInteraction::INTERACTION_TYPE_LIKE)
                ->first();

            if ($existingLike) {
                // 取消点赞
                $existingLike->delete();
                $work->decrement('like_count');
                $message = '取消点赞成功';
            } else {
                // 添加点赞
                WorkInteraction::create([
                    'work_id' => $work->id,
                    'user_id' => $user->id,
                    'interaction_type' => WorkInteraction::INTERACTION_TYPE_LIKE
                ]);
                $work->increment('like_count');
                $message = '点赞成功';
            }

            return $this->successResponse([
                'work_id' => $work->id,
                'like_count' => $work->like_count,
                'is_liked' => !$existingLike
            ], $message);

        } catch (\Exception $e) {
            Log::error('点赞操作失败', [
                'work_id' => $id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse(ApiCodeEnum::ERROR, '点赞操作失败：' . $e->getMessage());
        }
    }

    /**
     * 🔧 LongDev1标记：热门作品 - 按规范第2965行
     * @ApiTitle(热门作品)
     * @ApiMethod(GET)
     * @ApiRoute(/works/trending)
     */
    public function trending(Request $request)
    {
        try {
            $filters = $request->only(['work_type', 'period', 'per_page']);
            $period = $filters['period'] ?? 'week'; // day, week, month

            $query = WorkPlaza::published();

            // 时间范围筛选
            switch ($period) {
                case 'day':
                    $query->where('published_at', '>=', Carbon::now()->subDay());
                    break;
                case 'week':
                    $query->where('published_at', '>=', Carbon::now()->subWeek());
                    break;
                case 'month':
                    $query->where('published_at', '>=', Carbon::now()->subMonth());
                    break;
            }

            if (isset($filters['work_type'])) {
                $query->ofType($filters['work_type']);
            }

            // 按热度排序（综合浏览量、点赞量、分享量）
            $works = $query->selectRaw('*, (view_count * 1 + like_count * 3 + share_count * 5) as hot_score')
                          ->orderBy('hot_score', 'desc')
                          ->with(['user:id,username,avatar'])
                          ->paginate($filters['per_page'] ?? 20);

            return $this->successResponse([
                'works' => $works->items(),
                'period' => $period,
                'pagination' => [
                    'current_page' => $works->currentPage(),
                    'per_page' => $works->perPage(),
                    'total' => $works->total(),
                    'last_page' => $works->lastPage()
                ]
            ], '获取成功');

        } catch (\Exception $e) {
            Log::error('获取热门作品失败', [
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse(ApiCodeEnum::ERROR, '获取失败：' . $e->getMessage());
        }
    }


}
