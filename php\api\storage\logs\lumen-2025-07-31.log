[2025-07-31 03:07:11] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:12] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:12] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:13] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:13] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:14] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:14] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:15] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:16] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:16] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:16] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:17] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:18] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:18] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:18] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:18] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:19] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:20] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:20] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:48] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:07:53] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:01] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:01] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:02] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:03] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:03] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:03] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:04] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:11] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:12] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:14] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:14] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:15] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:17] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:19] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:19] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-07-31 03:08:25] production.ERROR: Static route "/api/config/batch" is shadowed by previously defined variable route "/api/config/([^/]+)" for method "PUT" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/config/batch\" is shadowed by previously defined variable route \"/api/config/([^/]+)\" for method \"PUT\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('PUT', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('PUT', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('PUT', '/api/config/bat...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
