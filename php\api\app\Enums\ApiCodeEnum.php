<?php

namespace App\Enums;

final class ApiCodeEnum
{
    const SUCCESS = 200;//成功
    const FAIL = 400;//操作失败
    const UNAUTHORIZED = 401;//未登录
    const FORBIDDEN = 403;//无权限
    const NOT_FOUND = 404;//内容不存在
    const METHOD_NOT_ALLOWED = 405;//网络请求方法错误
    const CONFLICT = 409;//状态冲突
    const VALIDATION_ERROR = 422;//验证错误
    const ERROR = 500;//发生异常
    const PERMISSION_DENIED = 403; // 权限被拒绝（别名）
    const CONNECTION_LIMIT = 1000; // 连接限制
    const INVALID_TOKEN = 1001;//TOKEN无效
    const EXPIRED = 1002; // 已过期
    const LIMIT_EXCEEDED = 1003; // 超出限制
    const USER_NOT_REGISTERED = 1004; // 用户未注册
    const USER_ALREADY_EXISTS = 1005; // 用户已存在
    const INSUFFICIENT_POINTS = 1006; // 积分不足
    const INVALID_OPERATION = 1007; // 无效操作
    const DUPLICATE_OPERATION = 1008; // 重复操作
    const EMAIL_ALREADY_EXISTS = 1009; // 邮箱已存在
    const INVALID_PARAMS = 1010; // 参数无效
    const INVALID_PARAMETER = 1010; // 参数无效（别名）
    const SYSTEM_ERROR = 1011; // 系统错误
    const SERVICE_UNAVAILABLE = 1012; // 服务不可用

    // AI服务相关状态码
    const AI_SERVICE_ERROR = 1011; // AI服务错误
    const GENERATION_FAILED = 1012; // 生成失败
    const MODEL_NOT_AVAILABLE = 1013; // 模型不可用
    const QUOTA_EXCEEDED = 1014; // 配额超限
    const CONTENT_FILTERED = 1015; // 内容被过滤
    const PROCESSING_TIMEOUT = 1016; // 处理超时

    // 文件相关状态码
    const FILE_NOT_FOUND = 1020; // 文件不存在
    const FILE_TOO_LARGE = 1021; // 文件过大
    const FILE_TYPE_NOT_SUPPORTED = 1022; // 文件类型不支持
    const UPLOAD_FAILED = 1023; // 上传失败

    // 项目相关状态码
    const PROJECT_NOT_FOUND = 1030; // 项目不存在
    const PROJECT_ACCESS_DENIED = 1031; // 项目访问被拒绝
    const PROJECT_LIMIT_EXCEEDED = 1032; // 项目数量超限

    // 角色相关状态码
    const CHARACTER_NOT_FOUND = 1040; // 角色不存在
    const CHARACTER_BINDING_FAILED = 1041; // 角色绑定失败
    const CHARACTER_LIMIT_EXCEEDED = 1042; // 角色数量超限

    // 其他业务状态码
    const EVENT_COUNT_LIMIT = 1091;//事件数已达限额
    const RATE_LIMIT_EXCEEDED = 1092; // 请求频率超限
    const MAINTENANCE_MODE = 1093; // 维护模式

    // 新增缺失的业务状态码
    const INTERNAL_ERROR = 1094; // 内部错误
    const MODEL_NOT_FOUND = 1095; // 模型不存在
    const INPUT_TOO_LONG = 1096; // 输入过长
    const BAD_REQUEST = 1097; // 错误请求

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            // 标准HTTP状态码
            self::SUCCESS => '成功',
            self::FAIL => '失败',
            self::UNAUTHORIZED => '未登录',
            self::FORBIDDEN => '无权限',
            self::NOT_FOUND => '内容不存在',
            self::METHOD_NOT_ALLOWED => '网络请求方法错误',
            self::CONFLICT => '状态冲突',
            self::VALIDATION_ERROR => '验证错误',
            self::ERROR => '发生异常',
            self::PERMISSION_DENIED => '权限被拒绝',

            // 基础业务状态码
            self::CONNECTION_LIMIT => '连接限制',
            self::INVALID_TOKEN => 'TOKEN无效',
            self::EXPIRED => '已过期',
            self::LIMIT_EXCEEDED => '超出限制',
            self::USER_NOT_REGISTERED => '用户未注册',
            self::USER_ALREADY_EXISTS => '用户已存在',
            self::INSUFFICIENT_POINTS => '积分不足',
            self::INVALID_OPERATION => '无效操作',
            self::DUPLICATE_OPERATION => '重复操作',
            self::EMAIL_ALREADY_EXISTS => '邮箱已存在',
            self::INVALID_PARAMS => '参数无效',
            self::INVALID_PARAMETER => '参数无效',
            self::SYSTEM_ERROR => '系统错误',
            self::SERVICE_UNAVAILABLE => '服务不可用',

            // AI服务相关状态码
            self::AI_SERVICE_ERROR => 'AI服务错误',
            self::GENERATION_FAILED => '生成失败',
            self::MODEL_NOT_AVAILABLE => '模型不可用',
            self::QUOTA_EXCEEDED => '配额超限',
            self::CONTENT_FILTERED => '内容被过滤',
            self::PROCESSING_TIMEOUT => '处理超时',

            // 文件相关状态码
            self::FILE_NOT_FOUND => '文件不存在',
            self::FILE_TOO_LARGE => '文件过大',
            self::FILE_TYPE_NOT_SUPPORTED => '文件类型不支持',
            self::UPLOAD_FAILED => '上传失败',

            // 项目相关状态码
            self::PROJECT_NOT_FOUND => '项目不存在',
            self::PROJECT_ACCESS_DENIED => '项目访问被拒绝',
            self::PROJECT_LIMIT_EXCEEDED => '项目数量超限',

            // 角色相关状态码
            self::CHARACTER_NOT_FOUND => '角色不存在',
            self::CHARACTER_BINDING_FAILED => '角色绑定失败',
            self::CHARACTER_LIMIT_EXCEEDED => '角色数量超限',

            // 其他业务状态码
            self::EVENT_COUNT_LIMIT => '事件数已达限额',
            self::RATE_LIMIT_EXCEEDED => '请求频率超限',
            self::MAINTENANCE_MODE => '维护模式',
            self::INTERNAL_ERROR => '内部错误',
            self::MODEL_NOT_FOUND => '模型不存在',
            self::INPUT_TOO_LONG => '输入过长',
            self::BAD_REQUEST => '错误请求',
        ];
    }

    /**
     * 根据状态码获取文字描述
     * @param int $code
     * @return string|null
     */
    public static function getDescription(int $code): ?string
    {
        $map = self::getMap();
        return $map[$code] ?? null; // 使用 ?? null 避免当码不存在时报错
    }
}
