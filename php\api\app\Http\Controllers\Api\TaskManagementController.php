<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\TaskManagementService;
use Illuminate\Http\Request;

/**
 * 任务管理与调度系统-向第三方AI平台（虚拟AI API服务）发起请求
 */
class TaskManagementController extends Controller
{
    protected $taskManagementService;

    public function __construct(TaskManagementService $taskManagementService)
    {
        $this->taskManagementService = $taskManagementService;
    }

    /**
     * @ApiTitle (取消任务)
     * @ApiSummary (取消正在进行的AI生成任务)
     * @ApiMethod (POST)
     * @ApiRoute (/tasks/{id}/cancel)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="任务ID")
     * @ApiParams (name="reason", type="string", required=false, description="取消原因")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.refund_amount", type="decimal", required=false, description="返还积分")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "任务已取消",
     *   "data": {
     *     "task_id": 123,
     *     "status": "cancelled",
     *     "refund_amount": "0.5000"
     *   }
     * })
     */
    public function cancelTask($id, Request $request)
    {
        $rules = [
            'reason' => 'sometimes|string|max:500'
        ];

        $messages = [
            'reason.max' => '取消原因不能超过500个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $reason = $request->get('reason', '用户主动取消');

        // 修复：将字符串ID转换为整数类型
        $taskId = (int) $id;
        if ($taskId <= 0) {
            return $this->formatResponse([
                'code' => 400,
                'message' => '任务ID无效',
                'data' => []
            ]);
        }

        $result = $this->taskManagementService->cancelTask($taskId, $user->id, $reason);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (重试任务)
     * @ApiSummary (重试失败的AI生成任务)
     * @ApiMethod (POST)
     * @ApiRoute (/tasks/{id}/retry)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="id", type="int", required=true, description="任务ID")
     * @ApiParams (name="platform", type="string", required=false, description="指定重试的AI平台")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.retry_count", type="int", required=true, description="重试次数")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "任务重试成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "retry_count": 1,
     *     "platform": "kling"
     *   }
     * })
     */
    public function retryTask($id, Request $request)
    {
        $rules = [
            'platform' => 'sometimes|string|in:deepseek,liblib,kling,minimax,volcengine'
        ];

        $messages = [
            'platform.in' => 'AI平台必须是：deepseek、liblib、kling、minimax、volcengine之一'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $platform = $request->get('platform');

        // 修复：将字符串ID转换为整数类型
        $taskId = (int) $id;
        if ($taskId <= 0) {
            return $this->errorResponse(ApiCodeEnum::INVALID_PARAMS, '任务ID无效');
        }

        $result = $this->taskManagementService->retryTask($taskId, $user->id, $platform);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle (批量任务状态查询)
     * @ApiSummary (查询多个任务的状态)
     * @ApiMethod (GET)
     * @ApiRoute (/batch/tasks/status)
     * @ApiHeaders (name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams (name="task_ids", type="string", required=true, description="任务ID列表，逗号分隔")
     * @ApiParams (name="batch_id", type="string", required=false, description="批量任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "tasks": [
     *       {
     *         "id": 123,
     *         "status": "completed",
     *         "task_type": "video_generation",
     *         "progress": 100
     *       },
     *       {
     *         "id": 124,
     *         "status": "processing",
     *         "task_type": "voice_synthesis",
     *         "progress": 60
     *       }
     *     ],
     *     "summary": {
     *       "total": 2,
     *       "completed": 1,
     *       "processing": 1,
     *       "failed": 0
     *     }
     *   }
     * })
     */
    public function getBatchStatus(Request $request)
    {
        // 使用AuthService进行认证 - 优先执行认证检查
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        // 参数验证 - 在认证通过后执行
        $rules = [
            'task_ids' => 'required_without:batch_id|string',
            'batch_id' => 'required_without:task_ids|string'
        ];

        $messages = [
            'task_ids.required_without' => '任务ID列表或批量任务ID必须提供其中一个',
            'batch_id.required_without' => '任务ID列表或批量任务ID必须提供其中一个'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        if ($request->has('task_ids')) {
            $taskIds = explode(',', $request->task_ids);
            $taskIds = array_map('intval', array_filter($taskIds));
            
            if (empty($taskIds)) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '任务ID列表格式错误');
            }

            $result = $this->taskManagementService->getBatchTaskStatus($taskIds, $user->id);
        } else {
            $result = $this->taskManagementService->getBatchTaskStatusByBatchId($request->batch_id, $user->id);
        }

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(获取超时配置)
     * @ApiSummary(获取任务超时配置信息)
     * @ApiMethod(GET)
     * @ApiRoute(/tasks/timeout-config)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "timeout_config": {
     *       "default_timeout": 300,
     *       "max_timeout": 1800,
     *       "retry_timeout": 60
     *     }
     *   }
     * })
     */
    public function getTimeoutConfig(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $result = $this->taskManagementService->getTimeoutConfig();
        
        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(查询任务恢复状态)
     * @ApiSummary(查询指定任务的恢复状态)
     * @ApiMethod(GET)
     * @ApiRoute(/tasks/{id}/recovery)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="任务ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "recovery_status": "可恢复",
     *     "can_recover": true,
     *     "recovery_options": ["重新开始", "从断点继续"]
     *   }
     * })
     */
    public function getRecoveryStatus($id, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        // 修复：将字符串ID转换为整数类型
        $taskId = (int) $id;
        if ($taskId <= 0) {
            return $this->errorResponse(ApiCodeEnum::INVALID_PARAMS, '任务ID无效');
        }

        $result = $this->taskManagementService->getRecoveryStatus($taskId, $user->id);
        
        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }


}
